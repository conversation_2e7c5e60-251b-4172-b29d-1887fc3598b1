# مولد المحتوى المدعوم بـ Gemini 2.5 Pro
import google.generativeai as genai
import random
import re
from datetime import datetime
import urllib.parse
from typing import List, Dict, Optional, Tuple
import json
import time
from .logger import logger
from .database import db
from .web_search import WebSearch
from .internal_links_manager import internal_links_manager
from .keywords_enhancer import keywords_enhancer
from .natural_writing_enhancer import natural_writing_enhancer
from .image_guard import image_guard, ai_image_generator
from config.settings import google_api_manager, BotConfig, ContentConfig, SEOConfig

class ContentGenerator:
    """مولد المحتوى الاحترافي للوكيل البرمجي"""
    
    def __init__(self):
        if not google_api_manager:
            raise Exception("Google API Key Manager is not initialized.")
        self.setup_gemini()
        self.web_search = WebSearch(
            search_engine_id=BotConfig.GOOGLE_SEARCH_ENGINE_ID
        )
        self.request_count = 0
        self.last_request_time = time.time()
        
        # أنماط الأخطاء الإملائية الطفيفة
        self.spelling_variations = {
            'ة': ['ه', 'ة'],
            'ي': ['ى', 'ي'],
            'أ': ['ا', 'أ'],
            'إ': ['ا', 'إ'],
            'ؤ': ['و', 'ؤ'],
            'ئ': ['ي', 'ئ']
        }
    
    def setup_gemini(self):
        """إعداد Gemini API"""
        try:
            # استخدام المفتاح من مدير المفاتيح
            current_key = google_api_manager.get_key()
            genai.configure(api_key=current_key)
            
            # إعداد النموذج
            generation_config = {
                "temperature": 0.8,
                "top_p": 0.95,
                "top_k": 64,
                "max_output_tokens": 8192,
            }
            
            safety_settings = [
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            ]
            
            self.model = genai.GenerativeModel(
                model_name="gemini-2.5-pro",  # استخدام Gemini 2.5 Pro فقط
                generation_config=generation_config,
                safety_settings=safety_settings
            )
            
            logger.info("✅ تم إعداد Gemini 2.5 Pro بنجاح")
            
        except Exception as e:
            logger.error("❌ فشل في إعداد Gemini API", e)
            raise
    
    def _respect_rate_limit(self):
        """احترام حدود معدل الطلبات لـ Gemini"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        # حد أقصى 60 طلب في الدقيقة
        if self.request_count >= 60 and time_since_last < 60:
            sleep_time = 60 - time_since_last
            logger.info(f"⏰ انتظار {sleep_time:.1f} ثانية لاحترام حدود Gemini API")
            time.sleep(sleep_time)
            self.request_count = 0
        
        if time_since_last >= 60:
            self.request_count = 0
        
        self.request_count += 1
        self.last_request_time = current_time
    
    def generate_article(self, source_content: Dict, content_type: str, dialect: str = "egyptian") -> Optional[Dict]:
        """توليد مقال احترافي من المحتوى المصدر، معزز ببحث الويب."""
        try:
            self._respect_rate_limit()

            # الخطوة 1: تحديد الألعاب الرئيسية من المصدر
            game_queries = self._extract_game_queries(source_content)

            # الخطوة 2: إجراء بحث ويب لكل لعبة
            search_results_summary = ""
            if game_queries:
                all_results = []
                for query in game_queries:
                    results = self.web_search.search(f"{query} game story and features")
                    if results:
                        all_results.extend(results)
                
                if all_results:
                    search_results_summary = "\n\n**ملخص نتائج بحث الويب (للاستخدام كمصدر أساسي):**\n"
                    for res in all_results:
                        search_results_summary += f"- **{res['title']}**: {res['snippet']}\n"
            
            # بناء البرومبت الشامل مع نتائج البحث
            prompt = self._build_article_prompt(source_content, content_type, dialect, search_results_summary)
            
            logger.info(f"🤖 بدء توليد مقال بنوع: {content_type} ولهجة: {dialect} (معزز بـ {len(game_queries)} عملية بحث)")
            
            # استدعاء Gemini
            response = self.model.generate_content(prompt)
            
            if not response or not response.text:
                logger.error("❌ لم يتم الحصول على استجابة من Gemini")
                return None
            
            # تحليل الاستجابة
            article_data = self._parse_gemini_response(response.text)
            
            if not article_data:
                logger.error("❌ فشل في تحليل استجابة Gemini")
                return None

            if 'error' in article_data:
                return article_data
            
            # تحسين المقال
            enhanced_article = self._enhance_article(article_data, source_content, content_type, dialect)

            # مراجعة جودة المقال
            quality_review = self._review_article_quality(enhanced_article)
            enhanced_article['quality_review'] = quality_review

            # تسجيل تحذيرات الجودة إذا وجدت
            if not quality_review['approved']:
                logger.warning(f"⚠️ مقال بجودة منخفضة: {quality_review['issues']}")
                logger.info(f"💡 اقتراحات التحسين: {quality_review['suggestions']}")

            # تسجيل استدعاء API
            logger.log_api_call("Gemini", "generate_content", "نجح")
            db.update_performance_stats(api_calls_gemini=1)

            logger.log_content_processing(
                content_type,
                source_content.get('source_url', 'غير محدد'),
                "توليد مقال",
                "نجح"
            )

            return enhanced_article
            
        except (Exception) as e:
            # A more specific exception handling for Google API errors
            from google.api_core.exceptions import ResourceExhausted, PermissionDenied
            if isinstance(e, (ResourceExhausted, PermissionDenied)):
                logger.warning(f"⚠️ خطأ في مفتاح Gemini API ({type(e).__name__}). جاري تبديل المفتاح...")
                try:
                    google_api_manager.rotate_key()
                    self.setup_gemini() # إعادة تهيئة Gemini بالمفتاح الجديد
                    logger.info("🔄 إعادة محاولة توليد المقال بالمفتاح الجديد...")
                    return self.generate_article(source_content, content_type, dialect)
                except Exception as rotation_error:
                    logger.critical(f"🚨 فشل في تبديل مفتاح API بعد خطأ أولي: {rotation_error}")
                    return None
            
            # General exception for other errors
            logger.error("❌ فشل في توليد المقال باستخدام Gemini", e)
            db.log_error("gemini_generation_error", str(e), source_content.get('source_url'))
            return None

    
    def _extract_game_queries(self, source_content: Dict) -> List[str]:
        """استخراج استعلامات البحث (أسماء الألعاب) من المحتوى المصدر."""
        text_to_analyze = f"{source_content.get('title', '')} {source_content.get('summary', '')}"
        # البحث عن كلمات تبدأ بحرف كبير وتتكون من كلمتين أو أكثر (نهج بسيط لتحديد الأسماء)
        game_names = re.findall(r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b', source_content.get('title', ''))
        
        # إضافة الكلمات المفتاحية كمصدر محتمل
        if not game_names:
            game_names = [kw for kw in source_content.get('keywords', []) if not any(c in 'اأإبتثجحخدذرزسشصضطظعغفقكلمنهوي' for c in kw)]

        # تنظيف وإزالة التكرار
        unique_games = list(set(game_names))
        logger.info(f"ℹ️ تم تحديد الألعاب المحتملة للبحث: {unique_games}")
        return unique_games[:3] # حد أقصى 3 ألعاب لتجنب استهلاك API المفرط

    
    def _build_enhanced_article_prompt(self, source_content: Dict, content_type: str, dialect: str, search_results: str = "") -> str:
        """بناء برومبت محسن لضمان التطابق بين العنوان والمحتوى"""
        
        # استخراج المعلومات الأساسية
        original_title = source_content.get('title', '')
        original_content = source_content.get('content', '')
        keywords = source_content.get('keywords', [])
        
        # تحليل العنوان لاستخراج المفاهيم الرئيسية
        title_concepts = self._extract_title_concepts(original_title)
        
        # بناء البرومبت المحسن
        enhanced_prompt = f"""
أنت كاتب محتوى احترافي متخصص في أخبار الألعاب. مهمتك كتابة مقال عالي الجودة يحقق التطابق الكامل بين العنوان والمحتوى.

**قواعد التطابق الإجبارية:**
1. يجب أن يغطي المحتوى جميع المفاهيم المذكورة في العنوان
2. لا تضع عنواناً يعد بمعلومات غير موجودة في المحتوى
3. إذا كان العنوان يذكر "دليل"، يجب أن يحتوي المحتوى على خطوات واضحة
4. إذا كان العنوان يذكر "مراجعة"، يجب أن يحتوي المحتوى على تقييم فعلي
5. إذا كان العنوان يذكر لعبة معينة، يجب أن يكون المحتوى عنها بالكامل

**المحتوى المصدر:**
العنوان الأصلي: {original_title}
المحتوى الأصلي: {original_content[:1000]}...
الكلمات المفتاحية: {', '.join(keywords)}

**المفاهيم الرئيسية التي يجب تغطيتها:**
{', '.join(title_concepts)}

**نتائج البحث الإضافية:**
{search_results}

**المطلوب:**
اكتب مقالاً باللهجة {dialect} يحتوي على:

1. **عنوان جذاب ودقيق** (30-60 حرف):
   - يعكس المحتوى الفعلي بدقة 100%
   - لا يعد بمعلومات غير موجودة
   - يحتوي على كلمات مفتاحية مهمة

2. **محتوى شامل** (800-1200 كلمة):
   - يغطي جميع المفاهيم المذكورة في العنوان
   - يحتوي على معلومات دقيقة ومفيدة
   - مكتوب بأسلوب شيق وسهل القراءة
   - يتضمن أسماء الألعاب بالإنجليزية والعربية

3. **ملخص قصير** (100-150 كلمة):
   - يلخص المحتوى الرئيسي
   - يحتوي على الكلمات المفتاحية

4. **كلمات مفتاحية** (5-8 كلمات):
   - مرتبطة بالمحتوى الفعلي
   - تساعد في SEO

5. **دعوة للعمل**:
   - تشجع القراء على التفاعل

**تحذير مهم:** سيتم رفض المقال إذا لم يحقق التطابق الكامل بين العنوان والمحتوى.

أرجع النتيجة بصيغة JSON:
{{
    "title": "العنوان المحسن",
    "content": "المحتوى الكامل",
    "summary": "الملخص",
    "keywords": ["كلمة1", "كلمة2", ...],
    "call_to_action": "دعوة للعمل"
}}
"""
        return enhanced_prompt
    
    def _extract_title_concepts(self, title: str) -> list:
        """استخراج المفاهيم الرئيسية من العنوان"""
        concepts = []
        
        # كلمات مفتاحية مهمة
        important_keywords = [
            'دليل', 'مراجعة', 'تحديث', 'إصدار', 'لعبة', 'ألعاب',
            'نصائح', 'حيل', 'استراتيجية', 'مقارنة', 'أفضل',
            'جديد', 'قادم', 'مجاني', 'مدفوع', 'تحميل'
        ]
        
        title_lower = title.lower()
        
        # استخراج الكلمات المهمة
        for keyword in important_keywords:
            if keyword in title_lower:
                concepts.append(keyword)
        
        # استخراج أسماء الألعاب (كلمات بالإنجليزية)
        english_words = re.findall(r'\b[A-Z][a-zA-Z]+\b', title)
        concepts.extend(english_words)
        
        # استخراج الأرقام (إصدارات، سنوات، إلخ)
        numbers = re.findall(r'\d+', title)
        concepts.extend(numbers)
        
        return list(set(concepts))  # إزالة التكرار

    def _build_article_prompt(self, source_content: Dict, content_type: str, dialect: str, search_results: str) -> str:
        """بناء البرومبت الشامل لتوليد المقال بناءً على القالب الاحترافي الجديد"""

        title = source_content.get('title', '')
        content = source_content.get('content', '')
        summary = source_content.get('summary', '')
        keywords = source_content.get('keywords', [])

        # تحسين اللهجات لتبدو أكثر طبيعية
        dialect_map = {
            "egyptian": "المصرية العامية (مع أخطاء إملائية بسيطة طبيعية)",
            "saudi": "السعودية العامية (مع أخطاء إملائية بسيطة طبيعية)",
            "standard": "العربية المبسطة (مع أخطاء إملائية بسيطة طبيعية)"
        }
        target_dialect = dialect_map.get(dialect, "العربية المبسطة (مع أخطاء إملائية بسيطة طبيعية)")

        prompt = f"""
مهمتك هي أن تكون كاتب محتوى وخبير ألعاب فيديو محترف جداً. هدفك هو إنشاء مقال مفصل، عميق، وموثوق يحترم ذكاء القارئ ويقدم له قيمة حقيقية.

**🔗 قواعد الروابط الداخلية الإجبارية:**
1. عند ذكر أي شركة ألعاب (مثل: Sony, Microsoft, Nintendo, Ubisoft, EA, Activision), ضع رابط وهمي: <a href="/company/[اسم-الشركة]">[اسم الشركة]</a>
2. عند ذكر أي لعبة (مثل: Call of Duty, FIFA, Assassin's Creed), ضع رابط وهمي: <a href="/game/[اسم-اللعبة]">[اسم اللعبة]</a>
3. عند ذكر أي منصة (مثل: PlayStation 5, Xbox Series X, Nintendo Switch), ضع رابط وهمي: <a href="/platform/[اسم-المنصة]">[اسم المنصة]</a>
4. عند ذكر أي شخصية مهمة في عالم الألعاب، ضع رابط وهمي: <a href="/person/[اسم-الشخص]">[اسم الشخص]</a>
5. **مهم جداً**: استخدم هذه الروابط في كل مرة تذكر فيها هذه الأسماء في المقال

**📝 قواعد الكتابة الطبيعية:**
1. اكتب بلهجة **{target_dialect}** بشكل طبيعي وعفوي
2. **أضف أخطاء إملائية بسيطة وطبيعية** (مثل: "هاذا" بدلاً من "هذا"، "الي" بدلاً من "التي"، "عشان" بدلاً من "لأن")
3. استخدم تعبيرات عامية مناسبة للهجة المختارة
4. اجعل الأسلوب يبدو كأنه مكتوب بواسطة إنسان حقيقي وليس AI

**المعلومات الأولية:**
- **العنوان الأصلي:** {title}
- **المحتوى/الملخص:** {summary or content}
- **الكلمات المفتاحية الأولية:** {', '.join(keywords)}
- **نتائج بحث الويب (للتدقيق):** {search_results or "لم يتم العثور على نتائج بحث، اعتمد على معرفتك العامة الموثوقة."}

**دليل كتابة المقالات الاحترافية (تعليمات صارمة يجب اتباعها):**

**المرحلة الأولى: التخطيط والبحث (قبل الكتابة)**
1.  **المصداقية والتطابق هما الأساس:**
    - **يجب أن يكون العنوان والمحتوى متطابقين تماماً.** إذا كان العنوان عن "تحديث Nintendo Switch"، فيجب أن يكون المحتوى بالكامل عن تحديثات Nintendo Switch وليس دليل للمبتدئين.
    - **لا تضلل القارئ أبداً.** إذا كان المقال عن توقعات، فاذكر ذلك بوضوح في العنوان والمحتوى.
    - **تحقق من معلوماتك:** استخدم نتائج البحث المتوفرة. إذا كانت المعلومة "شائعة" أو "تسريب"، فصفها كما هي ولا تقدمها كحقيقة مؤكدة.
    - **اكتب عنواناً يعكس المحتوى الفعلي:** إذا كان المحتوى دليل للمبتدئين، فليكن العنوان "دليل شامل للمبتدئين في عالم الألعاب" وليس "تحديث جديد: Nintendo Switch".
2.  **القيمة الحقيقية:**
    - قبل الكتابة، اسأل نفسك: "ما الذي سيستفيده القارئ من هذا المقال ولن يجده في مكان آخر؟".
    - **اشرح "لماذا"**: لا تكتفِ بذكر أن "اللعبة ممتعة". اشرح لماذا هي ممتعة، ما الذي يميزها، وكيف ترتبط بموضوع المقال.
    - **حافظ على التركيز:** إذا كان المقال عن موضوع محدد، لا تنحرف عنه إلى مواضيع أخرى.

**المرحلة الثانية: فن الصياغة (أثناء الكتابة)**
1.  **العنوان (Title):**
    - **يجب أن يعكس المحتوى الفعلي بدقة 100%**
    - إذا كان المحتوى الأصلي عن "دليل للمبتدئين"، فاكتب عنواناً مثل "دليل شامل للمبتدئين في عالم الألعاب"
    - إذا كان عن "تحديث لعبة"، فاكتب "تحديث جديد للعبة [اسم اللعبة]: كل ما تحتاج معرفته"
    - يجب أن يكون جذاباً، صادقاً، ومحسناً للـ SEO
    - يجب أن يتراوح طوله بين 50 و 70 حرفاً
2.  **الوصف التعريفي (Meta Description):** يجب أن يكون وصفاً دقيقاً ومختصراً للمحتوى الفعلي (حوالي 150-160 حرفاً). يجب أن يلخص ما سيجده القارئ فعلاً في المقال.
3.  **المقدمة:** ابدأ بجملة تخطف الانتباه وتوضح بدقة ما سيجده القارئ في هذا المقال تحديداً.
4.  **المحتوى العميق والمفيد:**
    - **التزم بموضوع المقال:** إذا كان العنوان عن موضوع محدد، فلا تنحرف عنه
    - لكل لعبة تذكرها، خصص لها قسماً كاملاً يشرحها بعمق ويربطها بموضوع المقال الرئيسي
    - **أسماء الأعلام:** **دائماً** اكتب أسماء الألعاب والشركات والتقنيات (مثل: 'PlayStation 5', 'Cyberpunk 2077', 'Unreal Engine') باللغة الإنجليزية. **لا تترجمها أبداً**
    - **الروابط الداخلية:** طبق قواعد الروابط المذكورة أعلاه بدقة في كل مرة تذكر فيها شركة أو لعبة أو منصة
5.  **سهولة القراءة والتنسيق الصحيح:**
    - استخدم فقرات قصيرة (3-4 أسطر).
    - استخدم القوائم النقطية بالرمز (-) والرقمية (1.).
    - **مهم جداً:** لا تستخدم رموز Markdown مثل ** أو # في النص النهائي. بدلاً من ذلك، استخدم HTML للتنسيق:
      * للنص العريض: استخدم <strong>النص</strong>
      * للعناوين الفرعية: استخدم <h3>العنوان</h3>
      * للقوائم: استخدم <ul><li>العنصر</li></ul>
    - **اللهجة:** اكتب بلهجة **{target_dialect}** بشكل طبيعي مع أخطاء إملائية بسيطة لتبدو أكثر إنسانية.
6.  **التدقيق الإملائي:** يجب أن يكون المقال خالياً تماماً من الأخطاء الإملائية والنحوية.

**المرحلة الثالثة: المراجعة والتحسين (بعد الكتابة)**
1.  **الخاتمة الفعّالة:**
    - لخّص أهم الأفكار في المقال.
    - **إلزامياً:** اختم المقال بـ "دعوة لاتخاذ إجراء" (Call to Action). اطلب من القارئ أن يشارك رأيه في التعليقات، أو اطرح عليه سؤالاً مباشراً يحفزه على التفاعل.

**هيكل المخرج النهائي (JSON إلزامي):**
يجب أن تكون إجابتك النهائية عبارة عن كائن JSON صالح فقط، بدون أي نصوص إضافية قبله أو بعده.

```json
{{
    "title": "اكتب هنا عنوان يعكس المحتوى الفعلي بدقة 100% - إذا كان المحتوى دليل للمبتدئين فاكتب 'دليل شامل للمبتدئين'، وإذا كان عن تحديث لعبة فاكتب 'تحديث جديد للعبة [اسم اللعبة]' (50-70 حرف)",
    "meta_description": "اكتب هنا وصف تعريفي دقيق للمحتوى الفعلي الذي سيجده القارئ في المقال (150-160 حرف).",
    "content": "اكتب هنا المحتوى الكامل للمقال بتنسيق HTML نظيف مع الروابط الداخلية المطلوبة. يجب أن يتطابق المحتوى 100% مع العنوان. طبق قواعد الروابط الداخلية لكل شركة ولعبة ومنصة تذكرها. اكتب بلهجة طبيعية مع أخطاء إملائية بسيطة. تجنب استخدام رموز Markdown واستخدم HTML للتنسيق.",
    "keywords": ["قائمة بالكلمات المفتاحية النهائية، شاملة أسماء الألعاب بالإنجليزية وأي مصطلحات تقنية ذات صلة"],
    "internal_links_used": ["قائمة بجميع الروابط الداخلية المستخدمة في المقال"],
    "category": "حدد هنا فئة المقال (مثل: 'مراجعات الألعاب', 'أخبار التحديثات', 'تحليلات', 'قوائم الألعاب')",
    "image_prompts": [
        "وصف دقيق ومفصل للصورة الرئيسية للمقال. يجب أن تكون جذابة وذات صلة مباشرة بالموضوع الرئيسي.",
        "وصف لصورة ثانوية متعلقة بأول لعبة مذكورة في المقال، تركز على ميزة تم شرحها.",
        "وصف لصورة أخرى متعلقة بثاني لعبة مذكورة في المقال، إن وجدت."
    ]
}}
```

**تذكير نهائي مهم جداً:**
- العنوان والمحتوى يجب أن يكونا متطابقين 100%
- إذا كان العنوان عن موضوع محدد، فالمحتوى يجب أن يكون عن نفس الموضوع بالضبط
- لا تكتب عنوان عن شيء ومحتوى عن شيء آخر

**ملاحظة هامة:** إذا كان المحتوى المصدر لا يتعلق بألعاب الفيديو، أرجع كائن JSON يحتوي على خطأ. مثال: `{{"error": "المحتوى المصدر لا يتعلق بألعاب الفيديو."}}`

**ابدأ الآن.**
"""
        return prompt
    
    
    def _parse_gemini_response(self, response_text: str) -> Optional[Dict]:
        """تحليل استجابة Gemini واستخراج البيانات"""
        try:
            # محاولة استخراج JSON من الاستجابة
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            
            if json_match:
                json_str = json_match.group(0)
                article_data = json.loads(json_str)
                
                # التحقق من وجود خطأ في الاستجابة
                if 'error' in article_data:
                    logger.warning(f"⚠️ رفض Gemini توليد المقال: {article_data['error']}")
                    return article_data # إرجاع الخطأ للمعالجة في main.py

                # التحقق من وجود الحقول المطلوبة
                required_fields = ['title', 'content']
                for field in required_fields:
                    if field not in article_data:
                        logger.warning(f"⚠️ حقل مفقود في استجابة Gemini: {field}")
                        # في هذه الحالة، قد يكون المحتوى غير صالح
                        return {'error': f'حقل مطلوب مفقود: {field}'}
                
                return article_data
            
            else:
                # في حالة عدم وجود JSON، محاولة تحليل النص العادي
                logger.warning("⚠️ لم يتم العثور على JSON في استجابة Gemini، محاولة تحليل النص")
                return self._parse_plain_text_response(response_text)
                
        except json.JSONDecodeError as e:
            logger.error("❌ فشل في تحليل JSON من استجابة Gemini", e)
            return self._parse_plain_text_response(response_text)
        except Exception as e:
            logger.error("❌ خطأ في تحليل استجابة Gemini", e)
            return None
    
    def _parse_plain_text_response(self, response_text: str) -> Optional[Dict]:
        """تحليل الاستجابة النصية العادية"""
        try:
            lines = response_text.strip().split('\n')
            
            # محاولة استخراج العنوان (أول سطر أو أول خط مميز)
            title = ""
            content_lines = []
            
            for i, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue
                
                if i == 0 or line.startswith('#') or line.startswith('**'):
                    # هذا على الأرجح العنوان
                    title = line.replace('#', '').replace('**', '').strip()
                else:
                    content_lines.append(line)
            
            content = '\n'.join(content_lines).strip()
            
            if not title and content_lines:
                # استخدام أول سطر كعنوان
                title = content_lines[0]
                content = '\n'.join(content_lines[1:]).strip()
            
            return {
                'title': title,
                'content': content,
                'meta_description': content[:150] if len(content) > 150 else content,
                'keywords': ['ألعاب', 'أخبار الألعاب'],
                'category': 'أخبار الألعاب',
                'image_prompts': [f"صورة ألعاب فيديو متعلقة بـ {title}"]
            }
            
        except Exception as e:
            logger.error("❌ فشل في تحليل الاستجابة النصية", e)
            return None
    
    def _enhance_article(self, article_data: Dict, source_content: Dict, content_type: str, dialect: str) -> Dict:
        """تحسين المقال النهائي مع ضمان التوافق بين العنوان والمحتوى"""

        # تحسين العنوان
        enhanced_title = self._enhance_title(article_data['title'])

        # تنظيف وتحسين المحتوى
        cleaned_content = self._clean_and_format_content(article_data['content'])

        # فحص التوافق بين العنوان والمحتوى وإصلاحه إذا لزم الأمر
        title_content_fixed = self._ensure_title_content_compatibility(enhanced_title, cleaned_content, source_content)
        final_title = title_content_fixed['title']
        final_content = title_content_fixed['content']

        # تحسين الكلمات المفتاحية
        enhanced_keywords = self._enhance_keywords(
            article_data.get('keywords', []),
            source_content.get('keywords', []),
            final_content
        )

        # تحسين الكلمات المفتاحية باستخدام النظام الجديد
        enhanced_keywords = keywords_enhancer.enhance_keywords(
            enhanced_keywords, final_content, content_type, 15
        )

        # تحسين الروابط الداخلية باستخدام النظام الجديد
        final_content, links_data = internal_links_manager.add_internal_links_to_content(final_content)

        # إضافة قسم الكلمات المفتاحية المنسق
        keywords_section = keywords_enhancer.format_keywords_section(enhanced_keywords)
        final_content = final_content + keywords_section

        # تحسين الكتابة الطبيعية (أخطاء إملائية + لهجة)
        final_content = natural_writing_enhancer.enhance_natural_writing(
            final_content, dialect, error_rate=0.25
        )

        # إضافة معلومات إضافية
        enhanced_article = {
            'title': final_title,
            'content': final_content,
            'meta_description': article_data.get('meta_description', final_title[:155]),
            'keywords': enhanced_keywords,
            'category': article_data.get('category', ContentConfig.CONTENT_TYPES[0]),
            'dialect': dialect,
            'content_type': content_type,
            'source_url': source_content.get('source_url'),
            'source_type': source_content.get('source_type'),
            'image_prompts': article_data.get('image_prompts', [f"صورة ألعاب فيديو متعلقة بـ {final_title}"]),
            'generated_at': datetime.now(),
            'word_count': len(final_content.split()),
            'title_content_fixed': title_content_fixed.get('was_fixed', False)
        }

        return enhanced_article

    def _ensure_title_content_compatibility(self, title: str, content: str, source_content: Dict) -> Dict:
        """ضمان التوافق بين العنوان والمحتوى بالتعديل التلقائي"""
        try:
            # فحص التوافق الحالي
            compatibility_analysis = self._analyze_title_content_match(title, content)

            if compatibility_analysis['match']:
                # التوافق موجود، لا حاجة للتعديل
                return {
                    'title': title,
                    'content': content,
                    'was_fixed': False,
                    'fix_type': 'none'
                }

            # التوافق غير موجود، نحتاج للإصلاح
            missing_concepts = compatibility_analysis.get('missing_concepts', [])
            logger.info(f"🔧 إصلاح عدم التوافق - المفاهيم المفقودة: {missing_concepts}")

            # تحديد نوع الإصلاح المطلوب
            fix_strategy = self._determine_fix_strategy(title, content, missing_concepts, source_content)

            if fix_strategy == 'enhance_content':
                # إضافة المفاهيم المفقودة للمحتوى
                fixed_content = self._enhance_content_with_missing_concepts(content, missing_concepts, source_content)
                return {
                    'title': title,
                    'content': fixed_content,
                    'was_fixed': True,
                    'fix_type': 'content_enhanced',
                    'added_concepts': missing_concepts
                }

            elif fix_strategy == 'adjust_title':
                # تعديل العنوان ليتناسب مع المحتوى
                adjusted_title = self._adjust_title_to_match_content(title, content, missing_concepts)
                return {
                    'title': adjusted_title,
                    'content': content,
                    'was_fixed': True,
                    'fix_type': 'title_adjusted',
                    'original_title': title
                }

            elif fix_strategy == 'both':
                # تعديل كلاهما
                enhanced_content = self._enhance_content_with_missing_concepts(content, missing_concepts[:2], source_content)
                adjusted_title = self._adjust_title_to_match_content(title, enhanced_content, missing_concepts[2:])
                return {
                    'title': adjusted_title,
                    'content': enhanced_content,
                    'was_fixed': True,
                    'fix_type': 'both_adjusted',
                    'original_title': title
                }

            else:
                # إذا فشل كل شيء، أعد المحتوى الأصلي
                logger.warning("⚠️ لم يتمكن من إصلاح التوافق، استخدام المحتوى الأصلي")
                return {
                    'title': title,
                    'content': content,
                    'was_fixed': False,
                    'fix_type': 'failed'
                }

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح التوافق: {e}")
            return {
                'title': title,
                'content': content,
                'was_fixed': False,
                'fix_type': 'error',
                'error': str(e)
            }

    def _determine_fix_strategy(self, title: str, content: str, missing_concepts: List[str], source_content: Dict) -> str:
        """تحديد استراتيجية الإصلاح المناسبة"""
        try:
            # تحليل طول المحتوى والعنوان
            content_length = len(content.split())
            title_length = len(title.split())

            # تحليل نوع المفاهيم المفقودة
            structural_concepts = ['دليل', 'مراجعة', 'نصائح', 'مقارنة', 'تحليل']
            descriptive_concepts = ['مبتدئين', 'متقدم', 'شامل', 'سريع', 'مفصل']
            game_specific_concepts = [concept for concept in missing_concepts
                                    if any(char.isupper() for char in concept)]  # أسماء الألعاب عادة تحتوي أحرف كبيرة

            missing_structural = [c for c in missing_concepts if c in structural_concepts]
            missing_descriptive = [c for c in missing_concepts if c in descriptive_concepts]
            missing_games = game_specific_concepts

            # قواعد اتخاذ القرار
            if len(missing_concepts) <= 2 and content_length > 200:
                # محتوى طويل ومفاهيم قليلة مفقودة - أضف للمحتوى
                return 'enhance_content'

            elif len(missing_concepts) > 4 or title_length > 15:
                # عنوان طويل أو مفاهيم كثيرة مفقودة - عدل العنوان
                return 'adjust_title'

            elif missing_structural and content_length < 300:
                # مفاهيم هيكلية مفقودة ومحتوى قصير - عدل العنوان
                return 'adjust_title'

            elif missing_descriptive or missing_games:
                # مفاهيم وصفية أو أسماء ألعاب - أضف للمحتوى
                return 'enhance_content'

            elif len(missing_concepts) > 2:
                # مفاهيم كثيرة - عدل كلاهما
                return 'both'

            else:
                # الحالة الافتراضية - أضف للمحتوى
                return 'enhance_content'

        except Exception as e:
            logger.warning(f"⚠️ خطأ في تحديد استراتيجية الإصلاح: {e}")
            return 'enhance_content'  # الاستراتيجية الافتراضية

    def _enhance_content_with_missing_concepts(self, content: str, missing_concepts: List[str], source_content: Dict) -> str:
        """تحسين المحتوى بإضافة المفاهيم المفقودة"""
        try:
            enhanced_content = content

            # إضافة فقرة تمهيدية إذا كانت مفقودة
            if any(concept in ['مبتدئين', 'دليل', 'شامل'] for concept in missing_concepts):
                intro_addition = self._create_intro_for_missing_concepts(missing_concepts, source_content)
                if intro_addition:
                    enhanced_content = f"{intro_addition}\n\n{enhanced_content}"

            # إضافة فقرات وسطية للمفاهيم المفقودة
            middle_additions = []
            for concept in missing_concepts:
                addition = self._create_content_for_concept(concept, source_content)
                if addition:
                    middle_additions.append(addition)

            if middle_additions:
                # إدراج الإضافات في منتصف المحتوى
                paragraphs = enhanced_content.split('\n\n')
                middle_index = len(paragraphs) // 2

                for i, addition in enumerate(middle_additions):
                    paragraphs.insert(middle_index + i, addition)

                enhanced_content = '\n\n'.join(paragraphs)

            # إضافة خاتمة إذا كانت مناسبة
            if any(concept in ['نصائح', 'خلاصة', 'ملخص'] for concept in missing_concepts):
                conclusion_addition = self._create_conclusion_for_missing_concepts(missing_concepts, source_content)
                if conclusion_addition:
                    enhanced_content = f"{enhanced_content}\n\n{conclusion_addition}"

            logger.info(f"✅ تم تحسين المحتوى بإضافة المفاهيم: {missing_concepts}")
            return enhanced_content

        except Exception as e:
            logger.error(f"❌ خطأ في تحسين المحتوى: {e}")
            return content

    def _adjust_title_to_match_content(self, title: str, content: str, missing_concepts: List[str]) -> str:
        """تعديل العنوان ليتناسب مع المحتوى"""
        try:
            # تحليل المحتوى لاستخراج المواضيع الرئيسية
            content_themes = self._extract_content_themes(content)

            # إزالة المفاهيم المفقودة من العنوان
            adjusted_title = title
            for concept in missing_concepts:
                # إزالة المفهوم وما يرتبط به
                patterns_to_remove = [
                    f"\\b{concept}\\b",
                    f"للـ{concept}",
                    f"لـ{concept}",
                    f"{concept}\\s+\\w+",
                    f"\\w+\\s+{concept}"
                ]

                for pattern in patterns_to_remove:
                    adjusted_title = re.sub(pattern, '', adjusted_title, flags=re.IGNORECASE)

            # تنظيف العنوان
            adjusted_title = re.sub(r'\s+', ' ', adjusted_title).strip()
            adjusted_title = re.sub(r'^[:\-\s]+|[:\-\s]+$', '', adjusted_title).strip()

            # إضافة مواضيع من المحتوى إذا كان العنوان قصير جداً
            if len(adjusted_title.split()) < 4 and content_themes:
                main_theme = content_themes[0]
                adjusted_title = f"{adjusted_title}: {main_theme}"

            # التأكد من أن العنوان ليس فارغاً
            if len(adjusted_title.strip()) < 10:
                # إنشاء عنوان جديد من المحتوى
                adjusted_title = self._generate_title_from_content(content, content_themes)

            logger.info(f"✅ تم تعديل العنوان من '{title}' إلى '{adjusted_title}'")
            return adjusted_title

        except Exception as e:
            logger.error(f"❌ خطأ في تعديل العنوان: {e}")
            return title

    def _create_intro_for_missing_concepts(self, missing_concepts: List[str], source_content: Dict) -> str:
        """إنشاء مقدمة للمفاهيم المفقودة"""
        try:
            intro_templates = {
                'مبتدئين': "إذا كنت مبتدئاً في عالم الألعاب، فهذا الدليل مصمم خصيصاً لك.",
                'دليل': "في هذا الدليل الشامل، سنستكشف جميع الجوانب المهمة.",
                'شامل': "سنقدم لك معلومات شاملة ومفصلة حول هذا الموضوع.",
                'نصائح': "إليك مجموعة من النصائح المفيدة والعملية.",
                'مراجعة': "في هذه المراجعة المفصلة، سنحلل جميع الجوانب."
            }

            relevant_intros = []
            for concept in missing_concepts:
                if concept in intro_templates:
                    relevant_intros.append(intro_templates[concept])

            if relevant_intros:
                return " ".join(relevant_intros[:2])  # أقصى جملتين

            return ""

        except Exception as e:
            logger.warning(f"⚠️ خطأ في إنشاء المقدمة: {e}")
            return ""

    def _create_content_for_concept(self, concept: str, source_content: Dict) -> str:
        """إنشاء محتوى للمفهوم المفقود"""
        try:
            concept_templates = {
                'مبتدئين': "للمبتدئين، من المهم فهم الأساسيات قبل الانتقال للمستويات المتقدمة.",
                'نصائح': "إليك بعض النصائح المفيدة التي ستساعدك في تحسين تجربتك.",
                'مقارنة': "عند المقارنة بين الخيارات المختلفة، يجب مراعاة عدة عوامل مهمة.",
                'تحليل': "من خلال التحليل المفصل، يمكننا فهم الجوانب المختلفة بشكل أفضل.",
                'شرح': "دعنا نشرح هذا الموضوع بطريقة واضحة ومفهومة."
            }

            if concept in concept_templates:
                return concept_templates[concept]

            # إذا كان المفهوم اسم لعبة أو مصطلح خاص
            if concept and len(concept) > 3:
                return f"بالنسبة لـ {concept}، هناك عدة نقاط مهمة يجب معرفتها."

            return ""

        except Exception as e:
            logger.warning(f"⚠️ خطأ في إنشاء محتوى للمفهوم: {e}")
            return ""

    def _create_conclusion_for_missing_concepts(self, missing_concepts: List[str], source_content: Dict) -> str:
        """إنشاء خاتمة للمفاهيم المفقودة"""
        try:
            conclusion_templates = {
                'نصائح': "نأمل أن تكون هذه النصائح مفيدة لك في رحلتك.",
                'خلاصة': "في الخلاصة، يمكننا القول أن هذا الموضوع يحتوي على جوانب متعددة مهمة.",
                'ملخص': "لتلخيص ما تم مناقشته، هناك نقاط رئيسية يجب تذكرها.",
                'مبتدئين': "للمبتدئين، الممارسة والصبر هما مفتاح النجاح.",
                'دليل': "هذا الدليل يوفر لك الأساس المتين للبدء."
            }

            relevant_conclusions = []
            for concept in missing_concepts:
                if concept in conclusion_templates:
                    relevant_conclusions.append(conclusion_templates[concept])

            if relevant_conclusions:
                return " ".join(relevant_conclusions[:1])  # جملة واحدة فقط

            return ""

        except Exception as e:
            logger.warning(f"⚠️ خطأ في إنشاء الخاتمة: {e}")
            return ""

    def _extract_content_themes(self, content: str) -> List[str]:
        """استخراج المواضيع الرئيسية من المحتوى"""
        try:
            # استخراج الكلمات المفتاحية من المحتوى
            words = re.findall(r'\b\w{4,}\b', content.lower())

            # كلمات مفتاحية متعلقة بالألعاب
            gaming_keywords = [
                'لعبة', 'ألعاب', 'لاعب', 'لاعبين', 'مستوى', 'مستويات',
                'شخصية', 'شخصيات', 'قصة', 'مغامرة', 'تحدي', 'مهمة',
                'استراتيجية', 'أكشن', 'محاكاة', 'رياضة', 'سباق'
            ]

            # العثور على المواضيع الشائعة
            word_freq = {}
            for word in words:
                if word in gaming_keywords or len(word) > 5:
                    word_freq[word] = word_freq.get(word, 0) + 1

            # ترتيب حسب التكرار
            sorted_themes = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)

            # إرجاع أفضل 3 مواضيع
            return [theme[0] for theme in sorted_themes[:3]]

        except Exception as e:
            logger.warning(f"⚠️ خطأ في استخراج المواضيع: {e}")
            return []

    def _generate_title_from_content(self, content: str, themes: List[str]) -> str:
        """إنشاء عنوان جديد من المحتوى"""
        try:
            # استخراج الجملة الأولى كأساس
            first_sentence = content.split('.')[0].strip()

            if len(first_sentence) > 50:
                first_sentence = first_sentence[:50] + "..."

            # إضافة موضوع رئيسي إذا كان متوفراً
            if themes:
                main_theme = themes[0]
                return f"{first_sentence} - دليل {main_theme}"

            return first_sentence

        except Exception as e:
            logger.warning(f"⚠️ خطأ في إنشاء العنوان: {e}")
            return "دليل الألعاب الشامل"

    def _clean_and_format_content(self, content: str) -> str:
        """تنظيف وتحسين تنسيق المحتوى"""
        if not content:
            return content

        # إزالة رموز Markdown الخاطئة
        content = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', content)
        content = re.sub(r'\*(.*?)\*', r'<em>\1</em>', content)

        # تحسين العناوين
        content = re.sub(r'^### (.*?)$', r'<h3>\1</h3>', content, flags=re.MULTILINE)
        content = re.sub(r'^## (.*?)$', r'<h2>\1</h2>', content, flags=re.MULTILINE)
        content = re.sub(r'^# (.*?)$', r'<h1>\1</h1>', content, flags=re.MULTILINE)

        # تحسين القوائم النقطية
        lines = content.split('\n')
        formatted_lines = []
        in_list = False

        for line in lines:
            line = line.strip()
            if line.startswith('- '):
                if not in_list:
                    formatted_lines.append('<ul>')
                    in_list = True
                formatted_lines.append(f'<li>{line[2:].strip()}</li>')
            elif line.startswith(('1. ', '2. ', '3. ', '4. ', '5. ', '6. ', '7. ', '8. ', '9. ')):
                if not in_list:
                    formatted_lines.append('<ol>')
                    in_list = True
                formatted_lines.append(f'<li>{line[3:].strip()}</li>')
            else:
                if in_list:
                    if formatted_lines and formatted_lines[-1] != '</ul>' and formatted_lines[-1] != '</ol>':
                        # تحديد نوع القائمة المناسب
                        if any('- ' in prev_line for prev_line in formatted_lines[-5:]):
                            formatted_lines.append('</ul>')
                        else:
                            formatted_lines.append('</ol>')
                    in_list = False
                if line:
                    formatted_lines.append(f'<p>{line}</p>')
                else:
                    formatted_lines.append('')

        # إغلاق القائمة إذا كانت مفتوحة
        if in_list:
            if any('- ' in line for line in lines[-5:]):
                formatted_lines.append('</ul>')
            else:
                formatted_lines.append('</ol>')

        # تنظيف المسافات الزائدة
        content = '\n'.join(formatted_lines)
        content = re.sub(r'\n\s*\n', '\n\n', content)
        content = re.sub(r'<p>\s*</p>', '', content)

        # إزالة الرموز الغريبة والأخطاء الشائعة
        content = content.replace('**', '')
        content = content.replace('##', '')
        content = content.replace('###', '')

        return content.strip()

    
    def _enhanced_quality_review(self, article: Dict) -> Dict:
        """مراجعة جودة محسنة مع فحص دقيق للتطابق"""
        issues = []
        suggestions = []
        warnings = []

        title = article.get('title', '')
        content = article.get('content', '')
        
        # 1. فحص التطابق المحسن بين العنوان والمحتوى
        title_content_analysis = self._analyze_title_content_match(title, content)
        
        if not title_content_analysis['match']:
            issues.append(f"Title-Content Mismatch - Missing: {', '.join(title_content_analysis['missing_concepts'])}")
            suggestions.append("تأكد من أن المحتوى يغطي جميع المواضيع المذكورة في العنوان")
            warnings.append("هذا المقال سيتم رفضه بسبب عدم التطابق")
        
        # 2. فحص جودة العنوان
        title_quality = self._analyze_title_quality(title)
        if title_quality['score'] < 7:
            issues.extend(title_quality['issues'])
            suggestions.extend(title_quality['suggestions'])
        
        # 3. فحص جودة المحتوى
        content_quality = self._analyze_content_quality(content)
        if content_quality['score'] < 7:
            issues.extend(content_quality['issues'])
            suggestions.extend(content_quality['suggestions'])
        
        # 4. فحص SEO
        seo_analysis = self._analyze_seo_quality(article)
        if seo_analysis['score'] < 6:
            issues.extend(seo_analysis['issues'])
            suggestions.extend(seo_analysis['suggestions'])
        
        # حساب النقاط النهائية
        base_score = 100
        
        # خصم كبير لعدم التطابق
        if not title_content_analysis['match']:
            base_score -= 50
        
        # خصم للمشاكل الأخرى
        base_score -= len(issues) * 5
        
        final_score = max(0, base_score)
        
        # قرار الموافقة
        approved = (
            title_content_analysis['match'] and  # يجب أن يكون هناك تطابق
            final_score >= 70 and  # نقاط كافية
            len(issues) <= 2  # مشاكل قليلة
        )
        
        return {
            'quality_score': final_score,
            'issues': issues,
            'suggestions': suggestions,
            'warnings': warnings,
            'approved': approved,
            'title_content_match': title_content_analysis['match'],
            'detailed_analysis': {
                'title_content': title_content_analysis,
                'title_quality': title_quality,
                'content_quality': content_quality,
                'seo_analysis': seo_analysis
            }
        }
    
    def _analyze_title_content_match(self, title: str, content: str) -> Dict:
        """تحليل ذكي ومرن للتطابق بين العنوان والمحتوى"""
        title_lower = title.lower()
        content_lower = content.lower()

        # استخراج المفاهيم من العنوان
        concepts = self._extract_title_concepts(title)

        missing_concepts = []
        found_concepts = []
        partial_matches = []

        for concept in concepts:
            concept_lower = concept.lower()

            # فحص وجود المفهوم مباشرة
            if concept_lower in content_lower:
                found_concepts.append(concept)
                continue

            # فحص مرادفات أو أشكال مختلفة
            synonyms = self._get_concept_synonyms(concept_lower)
            found_synonym = False

            for synonym in synonyms:
                if synonym in content_lower:
                    found_concepts.append(concept)
                    found_synonym = True
                    break

            if found_synonym:
                continue

            # فحص التطابق الجزئي (للكلمات الطويلة)
            if len(concept_lower) > 4:
                # فحص إذا كان جزء من الكلمة موجود
                if any(part in content_lower for part in [concept_lower[:4], concept_lower[-4:]]):
                    partial_matches.append(concept)
                    continue

            # فحص التطابق الصوتي أو المعنوي
            if self._check_semantic_match(concept_lower, content_lower):
                found_concepts.append(concept)
                continue

            missing_concepts.append(concept)

        # حساب نسبة التطابق مع مراعاة التطابق الجزئي
        if concepts:
            full_match_score = len(found_concepts) / len(concepts)
            partial_match_score = len(partial_matches) / len(concepts) * 0.5  # نصف نقطة للتطابق الجزئي
            total_match_ratio = full_match_score + partial_match_score
        else:
            total_match_ratio = 1.0  # إذا لم توجد مفاهيم محددة

        # تخفيف معايير التطابق للمقالات العامة
        required_ratio = 0.6 if len(concepts) <= 3 else 0.7  # معايير أكثر مرونة

        return {
            'match': total_match_ratio >= required_ratio,
            'match_ratio': total_match_ratio,
            'total_concepts': len(concepts),
            'found_concepts': found_concepts,
            'partial_matches': partial_matches,
            'missing_concepts': missing_concepts,
            'required_ratio': required_ratio
        }
    
    def _get_concept_synonyms(self, concept: str) -> list:
        """الحصول على مرادفات المفهوم - محسن"""
        synonyms_map = {
            'دليل': ['شرح', 'تعليم', 'كيفية', 'طريقة', 'خطوات', 'guide', 'tutorial', 'how to'],
            'مراجعة': ['تقييم', 'رأي', 'تحليل', 'نقد', 'review', 'analysis', 'opinion'],
            'تحديث': ['إصدار', 'نسخة', 'تطوير', 'تحسين', 'update', 'version', 'patch'],
            'نصائح': ['حيل', 'أسرار', 'استراتيجيات', 'طرق', 'tips', 'tricks', 'strategies'],
            'أفضل': ['أحسن', 'أجمل', 'أقوى', 'أروع', 'best', 'top', 'greatest'],
            'جديد': ['حديث', 'أحدث', 'جديدة', 'حديثة', 'new', 'latest', 'recent'],
            'مجاني': ['مجانية', 'بلا مقابل', 'مفتوح', 'free', 'gratis'],
            'مبتدئين': ['مبتدئ', 'جديد', 'بداية', 'أساسي', 'beginner', 'starter', 'basic', 'newcomer'],
            'متقدم': ['خبير', 'محترف', 'متمرس', 'advanced', 'expert', 'professional'],
            'لعبة': ['game', 'gaming', 'ألعاب', 'العاب'],
            'تجربة': ['experience', 'gameplay', 'لعب', 'أداء'],
            'مقارنة': ['comparison', 'مقابل', 'versus', 'vs'],
            'تحميل': ['download', 'تنزيل', 'حمل'],
            'مشاكل': ['problems', 'issues', 'bugs', 'عيوب', 'أخطاء'],
            'حلول': ['solutions', 'fixes', 'إصلاحات', 'طرق حل']
        }

        return synonyms_map.get(concept, [])

    def _check_semantic_match(self, concept: str, content: str) -> bool:
        """فحص التطابق المعنوي للمفاهيم"""
        # قواعد التطابق المعنوي
        semantic_rules = {
            'مبتدئين': ['بداية', 'أول', 'تعلم', 'أساسيات', 'البداية', 'للمبتدئين'],
            'دليل': ['كيف', 'طريقة', 'خطوات', 'تعليمات', 'شرح'],
            'مراجعة': ['تقييم', 'رأي', 'تجربة', 'انطباع'],
            'أفضل': ['ممتاز', 'رائع', 'عظيم', 'مميز', 'top'],
            'جديد': ['حديث', 'أحدث', 'latest', '2025', '2024'],
            'مجاني': ['free', 'بدون تكلفة', 'مفتوح المصدر']
        }

        if concept in semantic_rules:
            return any(rule in content for rule in semantic_rules[concept])

        return False
    
    def _analyze_title_quality(self, title: str) -> Dict:
        """تحليل جودة العنوان"""
        issues = []
        suggestions = []
        score = 10
        
        # فحص الطول
        if len(title) < 20:
            issues.append("العنوان قصير جداً")
            suggestions.append("اجعل العنوان أكثر وصفية")
            score -= 2
        elif len(title) > 80:
            issues.append("العنوان طويل جداً")
            suggestions.append("اختصر العنوان")
            score -= 1
        
        # فحص وجود كلمات جذابة
        attractive_words = ['أفضل', 'جديد', 'حصري', 'مجاني', 'سري', 'مذهل']
        if not any(word in title for word in attractive_words):
            suggestions.append("أضف كلمات جذابة للعنوان")
            score -= 1
        
        # فحص وجود أسماء ألعاب
        english_games = re.findall(r'\b[A-Z][a-zA-Z\s]+\b', title)
        if not english_games:
            suggestions.append("أضف اسم اللعبة بالإنجليزية")
            score -= 1
        
        return {
            'score': max(0, score),
            'issues': issues,
            'suggestions': suggestions
        }
    
    def _analyze_content_quality(self, content: str) -> Dict:
        """تحليل جودة المحتوى"""
        issues = []
        suggestions = []
        score = 10
        
        # فحص الطول
        if len(content) < 500:
            issues.append("المحتوى قصير جداً")
            suggestions.append("أضف المزيد من التفاصيل")
            score -= 3
        elif len(content) > 2000:
            suggestions.append("المحتوى طويل، تأكد من أنه مفيد")
        
        # فحص التنوع في الجمل
        sentences = content.split('.')
        if len(sentences) < 5:
            issues.append("قلة الجمل")
            suggestions.append("قسم المحتوى لجمل أكثر")
            score -= 1
        
        # فحص وجود دعوة للعمل
        cta_keywords = ['شاركنا', 'رأيك', 'تعليق', 'ما رأيكم']
        if not any(keyword in content for keyword in cta_keywords):
            suggestions.append("أضف دعوة للقراء للتفاعل")
            score -= 1
        
        return {
            'score': max(0, score),
            'issues': issues,
            'suggestions': suggestions
        }
    
    def _analyze_seo_quality(self, article: Dict) -> Dict:
        """تحليل جودة SEO"""
        issues = []
        suggestions = []
        score = 10
        
        title = article.get('title', '')
        content = article.get('content', '')
        keywords = article.get('keywords', [])
        
        # فحص الكلمات المفتاحية
        if len(keywords) < 3:
            issues.append("قلة الكلمات المفتاحية")
            suggestions.append("أضف المزيد من الكلمات المفتاحية")
            score -= 2
        
        # فحص وجود الكلمات المفتاحية في المحتوى
        keyword_density = 0
        for keyword in keywords:
            if keyword.lower() in content.lower():
                keyword_density += 1
        
        if keyword_density < len(keywords) * 0.5:
            issues.append("الكلمات المفتاحية غير موجودة في المحتوى")
            suggestions.append("تأكد من وجود الكلمات المفتاحية في المحتوى")
            score -= 2
        
        return {
            'score': max(0, score),
            'issues': issues,
            'suggestions': suggestions
        }

    def _review_article_quality(self, article: Dict) -> Dict:
        """مراجعة جودة المقال تلقائياً"""
        issues = []
        suggestions = []

        title = article.get('title', '')
        content = article.get('content', '')

        # فحص العنوان - استخدام الحدود الجديدة من الإعدادات
        if len(title) < SEOConfig.TITLE_LENGTH_MIN:
            issues.append("العنوان قصير جداً")
            suggestions.append("اجعل العنوان أكثر وصفية")
        elif len(title) > SEOConfig.TITLE_LENGTH_MAX:
            issues.append("العنوان طويل جداً")
            suggestions.append("اختصر العنوان ليكون أكثر جاذبية")

        # فحص التطابق بين العنوان والمحتوى (الأهم)
        title_lower = title.lower()
        content_lower = content.lower()

        # استخراج المفاهيم الرئيسية من العنوان
        title_concepts = []
        if 'nintendo switch' in title_lower:
            title_concepts.append('nintendo switch')
        if 'تحديث' in title_lower or 'update' in title_lower:
            title_concepts.append('تحديث')
        if 'دليل' in title_lower or 'guide' in title_lower:
            title_concepts.append('دليل')
        if 'مراجعة' in title_lower or 'review' in title_lower:
            title_concepts.append('مراجعة')
        if 'أفضل' in title_lower or 'best' in title_lower:
            title_concepts.append('أفضل')
        if 'مبتدئين' in title_lower or 'beginner' in title_lower:
            title_concepts.append('مبتدئين')
        if 'نصائح' in title_lower or 'tips' in title_lower:
            title_concepts.append('نصائح')

        # فحص وجود مفاهيم العنوان في المحتوى
        title_content_match = True
        missing_concepts = []

        for concept in title_concepts:
            if concept not in content_lower:
                title_content_match = False
                missing_concepts.append(concept)

        if not title_content_match and missing_concepts:
            issues.append(f"عدم تطابق بين العنوان والمحتوى - مفقود: {', '.join(missing_concepts)}")
            suggestions.append("تأكد من أن المحتوى يغطي جميع المواضيع المذكورة في العنوان")

        # فحص المحتوى
        if len(content) < 500:
            issues.append("المحتوى قصير جداً")
            suggestions.append("أضف المزيد من التفاصيل والتحليل")

        # فحص وجود رموز غريبة
        if '**' in content or '##' in content or '###' in content:
            issues.append("يحتوي على رموز تنسيق غير صحيحة")
            suggestions.append("تنظيف رموز Markdown")

        # فحص وجود أسماء ألعاب بالإنجليزية
        english_games = re.findall(r'\b[A-Z][a-zA-Z\s]+\b', content)
        if len(english_games) < 2:
            issues.append("قلة أسماء الألعاب بالإنجليزية")
            suggestions.append("أضف المزيد من أسماء الألعاب الصحيحة")

        # فحص وجود دعوة للعمل
        call_to_action_keywords = ['شاركنا', 'رأيك', 'تعليق', 'ما رأيكم', 'أخبرونا']
        has_cta = any(keyword in content for keyword in call_to_action_keywords)
        if not has_cta:
            issues.append("لا يحتوي على دعوة للعمل")
            suggestions.append("أضف دعوة للقراء للتفاعل")

        # حساب النقاط مع إعطاء وزن أكبر لمشكلة عدم التطابق
        quality_score = 100
        for issue in issues:
            if "عدم تطابق" in issue:
                quality_score -= 30  # خصم كبير لعدم التطابق
            else:
                quality_score -= 10  # خصم أقل للمشاكل الأخرى

        return {
            'quality_score': max(0, quality_score),
            'issues': issues,
            'suggestions': suggestions,
            'approved': len(issues) <= 2 and title_content_match,  # يجب أن يكون هناك تطابق للموافقة
            'title_content_match': title_content_match
        }
    
    def _enhance_title(self, original_title: str) -> str:
        """تحسين العنوان ليكون أكثر جاذبية"""
        # إزالة الكلمات غير المرغوب فيها من العنوان
        original_title = re.sub(r'\[.*?\]', '', original_title)
        original_title = re.sub(r'\(.*?\)', '', original_title)

        # التأكد من أن العنوان ضمن حدود الطول الموصى بها
        # تم تحسين منطق القطع ليكون أكثر ذكاءً
        if len(original_title) > SEOConfig.TITLE_LENGTH_MAX:
            # قطع ذكي عند آخر كلمة كاملة بدلاً من قطع عشوائي
            words = original_title.split()
            truncated = ""
            for word in words:
                if len(truncated + word + " ") <= SEOConfig.TITLE_LENGTH_MAX:  # بدون ترك مساحة للنقاط
                    truncated += word + " "
                else:
                    break
            return truncated.strip()  # إزالة النقاط الثلاث نهائياً

        if len(original_title) < SEOConfig.TITLE_LENGTH_MIN:
             return f"{original_title} | أخبار الألعاب"

        return original_title.strip()
    
    def _enhance_keywords(self, generated_keywords: List[str], source_keywords: List[str], content: str) -> List[str]:
        """تحسين الكلمات المفتاحية"""
        all_keywords = set(generated_keywords + source_keywords + ContentConfig.BASE_KEYWORDS)
        
        # إضافة كلمات مفتاحية من المحتوى
        content_words = re.findall(r'\b\w+\b', content.lower())
        word_freq = {}
        
        for word in content_words:
            if len(word) > 3:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # أخذ أكثر الكلمات تكراراً
        frequent_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:5]
        
        for word, freq in frequent_words:
            if freq > 2:
                all_keywords.add(word)
        
        return list(all_keywords)[:SEOConfig.MAX_KEYWORDS_PER_ARTICLE]
    
    def generate_image_prompt(self, article: Dict) -> str:
        """توليد وصف لصورة المقال"""
        title = article['title']
        content_type = article.get('content_type', 'عام')
        game_name = article.get('keywords', ['لعبة'])[0] # محاولة استخدام أول كلمة مفتاحية كاسم للعبة

        base_prompts = {
            'أخبار_التحديثات': f"صورة تُظهر التحديث الجديد للعبة {game_name} المتعلق بـ '{title}'، مع عناصر حديثة ومثيرة",
            'أخبار_المطورين': f"صورة تمثل فريق تطوير لعبة فيديو أو مكتب تطوير مع شعار اللعبة {game_name}",
            'الإضافات_الجديدة': f"صورة تُظهر إضافة جديدة أو مود للعبة فيديو مذكور في '{title}'",
            'الأسرار_المخفية': f"صورة غامضة ومثيرة من لعبة فيديو تلمح للسر المخفي في '{title}'",
            'المراجعات_التحليلات': f"صورة تحليلية للعبة فيديو تُظهر مقارنة أو تقييم متعلق بـ '{title}'"
        }
        
        prompt = base_prompts.get(content_type, f"صورة جميلة من عالم ألعاب الفيديو متعلقة بـ '{title}'")
        
        # إضافة تفاصيل إضافية
        prompt += ", بجودة عالية وألوان زاهية، مناسبة للنشر على المدونة"
        
        return prompt
    
    def optimize_for_seo(self, article: Dict) -> Dict:
        """تحسين المقال لمحركات البحث بطريقة متقدمة مع الحفاظ على التطابق بين العنوان والمحتوى"""
        content = article['content']
        keywords = article.get('keywords', [])
        title = article['title']

        # 1. تحسين العنوان مع الحفاظ على التطابق مع المحتوى
        # بدلاً من استبدال العنوان بالكامل، نحسنه فقط إذا كان قصيراً أو غير محسن
        optimized_title = title

        # فحص إذا كان العنوان يحتاج تحسين (قصير جداً أو لا يحتوي على كلمات مفتاحية)
        needs_optimization = (
            len(title) < SEOConfig.TITLE_LENGTH_MIN or
            not any(keyword.lower() in title.lower() for keyword in keywords[:3]) or
            not any(char in title for char in ['🎮', '🔥', '⚡', '🚀', '💎', '🏆', '📱', '🎯', '⭐', '🔍'])
        )

        if needs_optimization and SEOConfig.TITLE_TEMPLATES:
            # اختيار قالب مناسب بناءً على نوع المحتوى
            content_lower = content.lower()

            if 'مراجعة' in content_lower or 'تقييم' in content_lower or 'review' in content_lower:
                suitable_templates = [t for t in SEOConfig.TITLE_TEMPLATES if 'مراجعة' in t]
            elif 'تحديث' in content_lower or 'update' in content_lower:
                suitable_templates = [t for t in SEOConfig.TITLE_TEMPLATES if 'تحديث' in t]
            elif 'دليل' in content_lower or 'guide' in content_lower or 'نصائح' in content_lower:
                suitable_templates = [t for t in SEOConfig.TITLE_TEMPLATES if 'دليل' in t]
            elif 'أفضل' in content_lower or 'best' in content_lower or 'قائمة' in content_lower:
                suitable_templates = [t for t in SEOConfig.TITLE_TEMPLATES if 'أفضل' in t]
            else:
                suitable_templates = SEOConfig.TITLE_TEMPLATES

            if suitable_templates:
                template = random.choice(suitable_templates)
                # استخدام العنوان الأصلي كمحتوى بدلاً من الكلمة المفتاحية
                main_topic = title.replace('🎮', '').replace('🔥', '').replace('⚡', '').replace('🚀', '').strip()
                if ':' in main_topic:
                    main_topic = main_topic.split(':')[0].strip()

                optimized_title = template.format(content=main_topic)

        # التأكد من طول العنوان المناسب - تحسين منطق القطع
        if len(optimized_title) > SEOConfig.TITLE_LENGTH_MAX:
            # قطع ذكي عند آخر كلمة كاملة
            words = optimized_title.split()
            truncated = ""
            for word in words:
                if len(truncated + word + " ") <= SEOConfig.TITLE_LENGTH_MAX:
                    truncated += word + " "
                else:
                    break
            optimized_title = truncated.strip()  # إزالة النقاط الثلاث

        article['title'] = optimized_title

        # 2. إنشاء وصف تعريفي محسن ومتقدم
        if not article.get('meta_description'):
            # استخراج أول جملة مفيدة
            sentences = content.replace('<p>', '').replace('</p>', '').split('.')
            first_meaningful_sentence = ""

            for sentence in sentences:
                clean_sentence = sentence.strip()
                if len(clean_sentence) > 20 and not clean_sentence.startswith('<'):
                    first_meaningful_sentence = clean_sentence
                    break

            # إضافة كلمات مفتاحية عالية الأداء
            high_perf_keywords = [kw for kw in keywords if kw in SEOConfig.HIGH_PERFORMANCE_KEYWORDS]
            keyword_phrase = ', '.join(high_perf_keywords[:2]) if high_perf_keywords else ', '.join(keywords[:2])

            meta_description = f"{first_meaningful_sentence}. اكتشف المزيد حول {keyword_phrase} في دليلنا الشامل."
            article['meta_description'] = meta_description[:SEOConfig.META_DESCRIPTION_LENGTH]

        # 3. إضافة كلمات مفتاحية عالية الأداء
        enhanced_keywords = list(set(keywords + SEOConfig.HIGH_PERFORMANCE_KEYWORDS[:5]))
        article['keywords'] = enhanced_keywords[:SEOConfig.MAX_KEYWORDS_PER_ARTICLE]

        # 4. تحسين كثافة الكلمات المفتاحية
        optimized_content = self._optimize_keyword_density(content, article['keywords'])

        # 5. إضافة دعوة للعمل
        optimized_content = self._add_call_to_action(optimized_content)

        # 6. تحسين الهيكل
        optimized_content = self._improve_content_structure(optimized_content)

        article['content'] = optimized_content
        article['seo_optimized'] = True
        article['seo_score'] = self._calculate_seo_score(article)

        return article

    def _add_call_to_action(self, content: str) -> str:
        """إضافة دعوة للعمل في نهاية المقال"""
        if not content:
            return content

        # التحقق من وجود دعوة للعمل مسبقاً
        cta_keywords = ['شاركنا', 'رأيك', 'تعليق', 'ما رأيكم', 'أخبرونا']
        if any(keyword in content for keyword in cta_keywords):
            return content

        # إضافة دعوة للعمل عشوائية
        cta = random.choice(SEOConfig.CALL_TO_ACTION_PHRASES)
        return f"{content}\n\n<p><strong>{cta}</strong></p>"

    def _calculate_seo_score(self, article: Dict) -> int:
        """حساب نقاط SEO للمقال"""
        score = 0

        title = article.get('title', '')
        content = article.get('content', '')
        keywords = article.get('keywords', [])
        meta_desc = article.get('meta_description', '')

        # نقاط العنوان
        if SEOConfig.TITLE_LENGTH_MIN <= len(title) <= SEOConfig.TITLE_LENGTH_MAX:
            score += 20

        # نقاط الوصف التعريفي
        if 120 <= len(meta_desc) <= SEOConfig.META_DESCRIPTION_LENGTH:
            score += 15

        # نقاط الكلمات المفتاحية
        if 5 <= len(keywords) <= SEOConfig.MAX_KEYWORDS_PER_ARTICLE:
            score += 15

        # نقاط طول المحتوى
        word_count = len(content.split())
        if word_count >= 300:
            score += 20
        if word_count >= 500:
            score += 10

        # نقاط وجود دعوة للعمل
        cta_keywords = ['شاركنا', 'رأيك', 'تعليق', 'ما رأيكم', 'أخبرونا']
        if any(keyword in content for keyword in cta_keywords):
            score += 10

        # نقاط التنسيق
        if '<h' in content:  # وجود عناوين فرعية
            score += 10

        return min(score, 100)
    
    def _optimize_keyword_density(self, content: str, keywords: List[str]) -> str:
        """تحسين كثافة الكلمات المفتاحية"""
        if not keywords:
            return content
        
        words = content.split()
        total_words = len(words)
        
        for keyword in keywords[:3]:  # التركيز على أهم 3 كلمات مفتاحية
            current_count = content.lower().count(keyword.lower())
            target_count = max(1, int(total_words * SEOConfig.KEYWORD_DENSITY / 100))
            
            if current_count < target_count:
                # إضافة الكلمة المفتاحية بشكل طبيعي
                additional_needed = target_count - current_count
                content = self._naturally_insert_keyword(content, keyword, additional_needed)
        
        return content
    
    def _naturally_insert_keyword(self, content: str, keyword: str, count: int) -> str:
        """إدراج الكلمة المفتاحية بشكل طبيعي"""
        # تم تعطيل هذه الميزة مؤقتاً لمنع الحشو غير الطبيعي
        return content
    
    def _add_internal_links(self, content: str) -> str:
        """إضافة روابط داخلية محتملة"""
        # هذه وظيفة للمستقبل - يمكن ربطها بقاعدة بيانات المقالات السابقة
        return content
    
    def _improve_content_structure(self, content: str) -> str:
        """تحسين هيكل المحتوى"""
        # إضافة عناوين فرعية إذا لم تكن موجودة
        paragraphs = content.split('\n\n')

        if len(paragraphs) > 3:
            # إضافة عناوين فرعية في منتصف المحتوى
            middle_index = len(paragraphs) // 2
            paragraphs.insert(middle_index, "\n## نقاط مهمة:\n")

        return '\n\n'.join(paragraphs)







    async def generate_image_url(self, prompt: str, article_title: str = '') -> Optional[str]:
        """
        توليد رابط صورة آمنة وقانونية باستخدام ImageGuard Pro
        """
        try:
            logger.info(f"🖼️ بدء البحث عن صورة آمنة للموضوع: {prompt}")

            # استخدام ImageGuard Pro للبحث عن صورة آمنة
            image_data = await image_guard.search_safe_image(prompt, article_title)

            if image_data:
                image_url = image_data.get('url')
                source = image_data.get('source', 'Unknown')
                attribution = image_data.get('attribution', '')

                logger.info(f"✅ تم العثور على صورة آمنة من {source}")
                logger.info(f"📝 النسب: {attribution}")

                return image_url
            else:
                logger.warning("⚠️ فشل في العثور على صورة آمنة")
                return None

        except Exception as e:
            logger.error("❌ خطأ أثناء توليد رابط الصورة الآمنة", e)
            return None

    async def generate_image_with_metadata(self, prompt: str, article_title: str = '') -> Optional[Dict]:
        """
        توليد صورة مع البيانات الوصفية الكاملة
        """
        try:
            logger.info(f"🖼️ بدء البحث عن صورة مع البيانات الوصفية: {prompt}")

            # استخدام ImageGuard Pro للبحث عن صورة آمنة
            image_data = await image_guard.search_safe_image(prompt, article_title)

            if image_data:
                logger.info(f"✅ تم العثور على صورة آمنة من {image_data.get('source', 'Unknown')}")
                return image_data
            else:
                logger.warning("⚠️ فشل في العثور على صورة آمنة مع البيانات الوصفية")
                return None

        except Exception as e:
            logger.error("❌ خطأ أثناء توليد الصورة مع البيانات الوصفية", e)
            return None

    async def generate_ai_images_for_article(self, article_data: Dict, num_images: int = 3) -> List[Dict]:
        """
        إنشاء صور احترافية بالذكاء الاصطناعي للمقال
        """
        try:
            logger.info(f"🎨 بدء إنشاء {num_images} صورة احترافية بالذكاء الاصطناعي للمقال")

            # استخدام مولد الصور الجديد
            generated_images = await ai_image_generator.generate_article_images(article_data, num_images)

            if generated_images:
                logger.info(f"✅ تم إنشاء {len(generated_images)} صورة احترافية بنجاح")
                return generated_images
            else:
                logger.warning("⚠️ فشل في إنشاء الصور، استخدام الصور الاحتياطية")
                # العودة للطريقة التقليدية كخطة بديلة
                fallback_images = []
                for i in range(num_images):
                    fallback_image = await self.generate_image_with_metadata(
                        f"gaming {article_data.get('title', '')[:50]}",
                        article_data.get('title', '')
                    )
                    if fallback_image:
                        fallback_images.append(fallback_image)

                return fallback_images

        except Exception as e:
            logger.error(f"❌ فشل في إنشاء صور المقال بالذكاء الاصطناعي: {e}")
            return []

    async def create_professional_article_visuals(self, article_data: Dict) -> Dict:
        """
        إنشاء مجموعة شاملة من الصور الاحترافية للمقال
        """
        try:
            logger.info("🎨 إنشاء مجموعة شاملة من الصور الاحترافية...")

            visuals = {
                'main_images': [],
                'thumbnail': None,
                'social_media_images': [],
                'infographic_elements': [],
                'generation_stats': {}
            }

            # 1. إنشاء الصور الرئيسية (3 صور)
            main_images = await self.generate_ai_images_for_article(article_data, 3)
            visuals['main_images'] = main_images

            # 2. إنشاء صورة مصغرة مخصصة
            if main_images:
                # استخدام أول صورة كصورة مصغرة أو إنشاء واحدة مخصصة
                thumbnail_data = article_data.copy()
                thumbnail_data['title'] = f"Thumbnail: {article_data.get('title', '')}"

                thumbnail_images = await ai_image_generator.generate_article_images(thumbnail_data, 1)
                if thumbnail_images:
                    visuals['thumbnail'] = thumbnail_images[0]
                else:
                    visuals['thumbnail'] = main_images[0]  # استخدام الصورة الأولى

            # 3. إنشاء صور وسائل التواصل الاجتماعي
            social_media_data = article_data.copy()
            social_media_data['title'] = f"Social Media: {article_data.get('title', '')}"

            social_images = await ai_image_generator.generate_article_images(social_media_data, 2)
            visuals['social_media_images'] = social_images

            # 4. إحصائيات الإنشاء
            visuals['generation_stats'] = ai_image_generator.get_generation_stats()

            total_images = len(visuals['main_images']) + len(visuals['social_media_images'])
            if visuals['thumbnail']:
                total_images += 1

            logger.info(f"✅ تم إنشاء {total_images} صورة احترافية للمقال")

            return visuals

        except Exception as e:
            logger.error(f"❌ فشل في إنشاء الصور الاحترافية الشاملة: {e}")
            return {
                'main_images': [],
                'thumbnail': None,
                'social_media_images': [],
                'infographic_elements': [],
                'generation_stats': {}
            }
