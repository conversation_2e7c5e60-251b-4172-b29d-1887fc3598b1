# -*- coding: utf-8 -*-
"""
تكوين APIs الجديدة لتوليد الصور
يحتوي على إعدادات OpenArt، Leap AI، DeepAI، و Replicate
"""

import os
from typing import Dict, List, Optional
from dataclasses import dataclass

@dataclass
class APIConfig:
    """تكوين API واحد"""
    name: str
    base_url: str
    api_key: str
    enabled: bool = True
    priority: int = 1
    max_requests_per_day: int = 100
    timeout: int = 60
    retry_attempts: int = 3

class NewImageAPIsConfig:
    """مدير تكوين APIs الجديدة لتوليد الصور"""
    
    def __init__(self):
        # تحميل مفاتيح API من متغيرات البيئة
        self.openart_api_key = os.getenv('OPENART_API_KEY', '')
        self.leap_ai_api_key = os.getenv('LEAP_AI_API_KEY', '')
        self.deepai_api_key = os.getenv('DEEPAI_API_KEY', '')
        self.replicate_api_key = os.getenv('REPLICATE_API_KEY', '')
        
        # تكوين APIs
        self.apis = {
            'openart': APIConfig(
                name='OpenArt',
                base_url='https://api.openart.ai/v1',
                api_key=self.openart_api_key,
                enabled=bool(self.openart_api_key),
                priority=1,
                max_requests_per_day=100,
                timeout=60,
                retry_attempts=3
            ),
            'leap_ai': APIConfig(
                name='Leap AI',
                base_url='https://api.tryleap.ai/api/v1',
                api_key=self.leap_ai_api_key,
                enabled=bool(self.leap_ai_api_key),
                priority=2,
                max_requests_per_day=200,
                timeout=90,
                retry_attempts=3
            ),
            'deepai': APIConfig(
                name='DeepAI',
                base_url='https://api.deepai.org/api',
                api_key=self.deepai_api_key,
                enabled=bool(self.deepai_api_key),
                priority=3,
                max_requests_per_day=500,
                timeout=45,
                retry_attempts=2
            ),
            'replicate': APIConfig(
                name='Replicate',
                base_url='https://api.replicate.com/v1',
                api_key=self.replicate_api_key,
                enabled=bool(self.replicate_api_key),
                priority=4,
                max_requests_per_day=1000,
                timeout=120,
                retry_attempts=3
            )
        }
        
        # إعدادات عامة
        self.general_settings = {
            'default_width': 1024,
            'default_height': 1024,
            'default_quality': 'high',
            'max_prompt_length': 500,
            'enable_prompt_enhancement': True,
            'enable_nsfw_filter': True,
            'enable_caching': True,
            'cache_duration_hours': 24,
            'fallback_to_existing_system': True
        }
        
        # قوالب الـ prompts المحسنة
        self.prompt_templates = {
            'gaming': {
                'prefix': 'high-quality gaming artwork, ',
                'suffix': ', professional digital art, 4k resolution, detailed',
                'style_keywords': ['gaming', 'digital art', 'concept art', 'illustration']
            },
            'news': {
                'prefix': 'news article illustration, ',
                'suffix': ', clean design, professional, modern style',
                'style_keywords': ['news', 'article', 'illustration', 'modern']
            },
            'review': {
                'prefix': 'game review illustration, ',
                'suffix': ', detailed artwork, high quality, professional',
                'style_keywords': ['review', 'game', 'detailed', 'professional']
            }
        }
        
        # إعدادات خاصة لكل API
        self.api_specific_settings = {
            'openart': {
                'model': 'openart-v4',
                'style': 'realistic',
                'aspect_ratio': '1:1',
                'guidance_scale': 7.5,
                'num_inference_steps': 20
            },
            'leap_ai': {
                'model': 'stable-diffusion-v2-1',
                'style': 'digital-art',
                'guidance_scale': 7.0,
                'num_inference_steps': 25,
                'scheduler': 'ddim'
            },
            'deepai': {
                'model': 'text2img',
                'style': 'hd',
                'grid_size': '1',
                'width': 1024,
                'height': 1024
            },
            'replicate': {
                'model': 'stability-ai/stable-diffusion:27b93a2413e7f36cd83da926f3656280b2931564ff050bf9575f1fdf9bcd7478',
                'guidance_scale': 7.5,
                'num_inference_steps': 20,
                'scheduler': 'K_EULER'
            }
        }
    
    def get_enabled_apis(self) -> List[APIConfig]:
        """الحصول على قائمة APIs المفعلة مرتبة حسب الأولوية"""
        enabled_apis = [api for api in self.apis.values() if api.enabled]
        return sorted(enabled_apis, key=lambda x: x.priority)
    
    def get_api_config(self, api_name: str) -> Optional[APIConfig]:
        """الحصول على تكوين API محدد"""
        return self.apis.get(api_name)
    
    def is_api_available(self, api_name: str) -> bool:
        """فحص ما إذا كان API متاح ومفعل"""
        api = self.apis.get(api_name)
        return api is not None and api.enabled and bool(api.api_key)
    
    def get_api_headers(self, api_name: str) -> Dict[str, str]:
        """الحصول على headers المطلوبة لـ API محدد"""
        api = self.get_api_config(api_name)
        if not api:
            return {}
        
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'GamingNewsBot/2.0'
        }
        
        # إضافة header المفتاح حسب نوع API
        if api_name == 'openart':
            headers['Authorization'] = f'Bearer {api.api_key}'
        elif api_name == 'leap_ai':
            headers['Authorization'] = f'Bearer {api.api_key}'
        elif api_name == 'deepai':
            headers['api-key'] = api.api_key
        elif api_name == 'replicate':
            headers['Authorization'] = f'Token {api.api_key}'
        
        return headers
    
    def enhance_prompt(self, prompt: str, category: str = 'gaming') -> str:
        """تحسين الـ prompt باستخدام القوالب"""
        if not self.general_settings['enable_prompt_enhancement']:
            return prompt
        
        template = self.prompt_templates.get(category, self.prompt_templates['gaming'])
        
        # تنظيف الـ prompt
        clean_prompt = prompt.strip()
        if len(clean_prompt) > self.general_settings['max_prompt_length']:
            clean_prompt = clean_prompt[:self.general_settings['max_prompt_length']]
        
        # تطبيق القالب
        enhanced_prompt = f"{template['prefix']}{clean_prompt}{template['suffix']}"
        
        return enhanced_prompt
    
    def get_fallback_settings(self) -> Dict:
        """الحصول على إعدادات النظام الاحتياطي"""
        return {
            'use_existing_system': self.general_settings['fallback_to_existing_system'],
            'fallback_apis': ['pollinations', 'freepik', 'fluxai'],
            'fallback_timeout': 30
        }

# إنشاء كائن التكوين العام
new_image_apis_config = NewImageAPIsConfig()
