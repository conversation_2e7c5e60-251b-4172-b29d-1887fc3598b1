#!/usr/bin/env python3
"""
اختبار سريع للتحسينات الأساسية
"""

import asyncio
import os
import sys
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger


async def quick_test():
    """اختبار سريع للتحسينات"""
    print("🚀 اختبار سريع للتحسينات الجديدة")
    print("="*50)
    
    # اختبار 1: الروابط المحسنة
    print("\n🔗 اختبار 1: نظام الروابط المحسن")
    try:
        from modules.internal_links_manager import internal_links_manager
        
        test_text = "شركة Sony تعلن عن تحديث للعبة God of War على PlayStation."
        enhanced_content, links_data = internal_links_manager.add_internal_links_to_content(test_text)
        
        print(f"✅ تم إضافة {links_data['total_links']} رابط")
        print(f"📝 النص المحسن: {enhanced_content[:100]}...")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الروابط: {e}")
    
    # اختبار 2: إنشاء صورة للمقال العام
    print("\n🎨 اختبار 2: إنشاء صورة للمقال العام")
    try:
        from modules.general_article_image_generator import general_article_image_generator
        
        test_article = {
            'title': 'أفضل 3 ألعاب مجانية على Steam',
            'content': 'قائمة بأفضل الألعاب المجانية تشمل Dota 2 و Counter-Strike',
            'keywords': ['ألعاب مجانية', 'Steam']
        }
        
        print("🎨 جاري إنشاء صورة...")
        image_result = await general_article_image_generator.generate_image_for_general_article(test_article)
        
        if image_result:
            print(f"✅ تم إنشاء صورة: {image_result.get('filename', 'غير محدد')}")
        else:
            print("⚠️ لم يتم إنشاء صورة (قد يكون بسبب عدم توفر الإنترنت)")
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء الصورة: {e}")
    
    # اختبار 3: التركيب الذكي
    print("\n🎭 اختبار 3: التركيب الذكي")
    try:
        from modules.smart_image_compositor import smart_image_compositor
        
        test_article = {
            'title': 'مراجعة لعبة Minecraft',
            'content': 'تحليل شامل للعبة Minecraft',
            'keywords': ['Minecraft', 'مراجعة']
        }
        
        print("🎭 جاري إنشاء تركيب ذكي...")
        composite_result = await smart_image_compositor.create_smart_composite(test_article, "Minecraft")
        
        if composite_result:
            print(f"✅ تم إنشاء تركيب: {composite_result.get('filename', 'غير محدد')}")
        else:
            print("⚠️ لم يتم إنشاء تركيب (قد يكون بسبب عدم توفر الإنترنت)")
            
    except Exception as e:
        print(f"❌ خطأ في التركيب: {e}")
    
    print("\n" + "="*50)
    print("🎉 انتهى الاختبار السريع!")
    print("💡 للاختبار الشامل، شغل: python run_complete_test.py")
    print("="*50)


if __name__ == "__main__":
    # إنشاء المجلدات المطلوبة
    os.makedirs('images/general_articles', exist_ok=True)
    os.makedirs('images/smart_composite', exist_ok=True)
    os.makedirs('temp/image_processing', exist_ok=True)
    
    asyncio.run(quick_test())
