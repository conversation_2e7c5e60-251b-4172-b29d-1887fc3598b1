# نظام إدارة الروابط الداخلية الذكي
import sqlite3
import re
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from .logger import logger
from .database import db

class InternalLinksManager:
    """مدير الروابط الداخلية الذكي"""
    
    def __init__(self):
        self.db_path = "data/articles.db"
        self._init_links_database()
        
        # قواعد بيانات الكيانات
        self.companies_db = {}
        self.games_db = {}
        self.platforms_db = {}
        self.persons_db = {}
        
        # تحميل قواعد البيانات
        self._load_entities_database()
    
    def _init_links_database(self):
        """إنشاء جداول الروابط الداخلية"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول الكيانات (الشركات، الألعاب، المنصات، الأشخاص)
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS entities (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL UNIQUE,
                        type TEXT NOT NULL,
                        slug TEXT NOT NULL UNIQUE,
                        description TEXT,
                        first_mentioned_article_id INTEGER,
                        mention_count INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # جدول الروابط الداخلية المستخدمة
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS internal_links_usage (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        article_id INTEGER NOT NULL,
                        entity_id INTEGER NOT NULL,
                        anchor_text TEXT NOT NULL,
                        link_url TEXT NOT NULL,
                        position_in_content INTEGER,
                        context_before TEXT,
                        context_after TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (article_id) REFERENCES published_articles (id),
                        FOREIGN KEY (entity_id) REFERENCES entities (id)
                    )
                ''')
                
                # جدول إحصائيات الروابط
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS link_statistics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        article_id INTEGER NOT NULL,
                        total_internal_links INTEGER DEFAULT 0,
                        unique_entities_linked INTEGER DEFAULT 0,
                        companies_linked INTEGER DEFAULT 0,
                        games_linked INTEGER DEFAULT 0,
                        platforms_linked INTEGER DEFAULT 0,
                        persons_linked INTEGER DEFAULT 0,
                        link_density REAL DEFAULT 0.0,
                        analysis_date DATE DEFAULT CURRENT_DATE,
                        FOREIGN KEY (article_id) REFERENCES published_articles (id)
                    )
                ''')
                
                conn.commit()
                logger.info("✅ تم إنشاء جداول الروابط الداخلية")
                
        except Exception as e:
            logger.error("❌ فشل في إنشاء جداول الروابط الداخلية", e)
    
    def _load_entities_database(self):
        """تحميل قواعد بيانات الكيانات"""
        try:
            # شركات الألعاب
            self.companies_db = {
                'Sony': {'slug': 'sony', 'type': 'company', 'description': 'شركة سوني اليابانية'},
                'Microsoft': {'slug': 'microsoft', 'type': 'company', 'description': 'شركة مايكروسوفت'},
                'Nintendo': {'slug': 'nintendo', 'type': 'company', 'description': 'شركة نينتندو اليابانية'},
                'Ubisoft': {'slug': 'ubisoft', 'type': 'company', 'description': 'شركة يوبي سوفت الفرنسية'},
                'Electronic Arts': {'slug': 'electronic-arts', 'type': 'company', 'description': 'شركة إلكترونيك آرتس'},
                'EA': {'slug': 'electronic-arts', 'type': 'company', 'description': 'شركة إلكترونيك آرتس'},
                'Activision': {'slug': 'activision', 'type': 'company', 'description': 'شركة أكتيفيجن'},
                'Blizzard': {'slug': 'blizzard', 'type': 'company', 'description': 'شركة بليزارد'},
                'Epic Games': {'slug': 'epic-games', 'type': 'company', 'description': 'شركة إيبك جيمز'},
                'Valve': {'slug': 'valve', 'type': 'company', 'description': 'شركة فالف'},
                'Rockstar': {'slug': 'rockstar', 'type': 'company', 'description': 'شركة روك ستار'},
                'CD Projekt': {'slug': 'cd-projekt', 'type': 'company', 'description': 'شركة سي دي بروجكت'},
                'Bethesda': {'slug': 'bethesda', 'type': 'company', 'description': 'شركة بيثيسدا'},
                'Square Enix': {'slug': 'square-enix', 'type': 'company', 'description': 'شركة سكوير إنيكس'},
                'Capcom': {'slug': 'capcom', 'type': 'company', 'description': 'شركة كابكوم اليابانية'},
                'Konami': {'slug': 'konami', 'type': 'company', 'description': 'شركة كونامي اليابانية'},
                'Bandai Namco': {'slug': 'bandai-namco', 'type': 'company', 'description': 'شركة باندي نامكو'},
                'Sega': {'slug': 'sega', 'type': 'company', 'description': 'شركة سيجا اليابانية'},
                'Take-Two': {'slug': 'take-two', 'type': 'company', 'description': 'شركة تيك تو'},
                'Riot Games': {'slug': 'riot-games', 'type': 'company', 'description': 'شركة رايوت جيمز'}
            }
            
            # منصات الألعاب
            self.platforms_db = {
                'PlayStation 5': {'slug': 'playstation-5', 'type': 'platform', 'description': 'جهاز بلايستيشن 5'},
                'PS5': {'slug': 'playstation-5', 'type': 'platform', 'description': 'جهاز بلايستيشن 5'},
                'PlayStation 4': {'slug': 'playstation-4', 'type': 'platform', 'description': 'جهاز بلايستيشن 4'},
                'PS4': {'slug': 'playstation-4', 'type': 'platform', 'description': 'جهاز بلايستيشن 4'},
                'Xbox Series X': {'slug': 'xbox-series-x', 'type': 'platform', 'description': 'جهاز إكس بوكس سيريز إكس'},
                'Xbox Series S': {'slug': 'xbox-series-s', 'type': 'platform', 'description': 'جهاز إكس بوكس سيريز إس'},
                'Xbox One': {'slug': 'xbox-one', 'type': 'platform', 'description': 'جهاز إكس بوكس ون'},
                'Nintendo Switch': {'slug': 'nintendo-switch', 'type': 'platform', 'description': 'جهاز نينتندو سويتش'},
                'PC': {'slug': 'pc', 'type': 'platform', 'description': 'الكمبيوتر الشخصي'},
                'Steam': {'slug': 'steam', 'type': 'platform', 'description': 'منصة ستيم'},
                'Epic Games Store': {'slug': 'epic-games-store', 'type': 'platform', 'description': 'متجر إيبك جيمز'},
                'Origin': {'slug': 'origin', 'type': 'platform', 'description': 'منصة أوريجن'},
                'Battle.net': {'slug': 'battle-net', 'type': 'platform', 'description': 'منصة باتل نت'},
                'GOG': {'slug': 'gog', 'type': 'platform', 'description': 'منصة جي أو جي'},
                'Uplay': {'slug': 'uplay', 'type': 'platform', 'description': 'منصة يوبلاي'}
            }
            
            # الألعاب الشهيرة
            self.games_db = {
                'Call of Duty': {'slug': 'call-of-duty', 'type': 'game', 'description': 'سلسلة ألعاب كول أوف ديوتي'},
                'FIFA': {'slug': 'fifa', 'type': 'game', 'description': 'سلسلة ألعاب فيفا'},
                'Assassin\'s Creed': {'slug': 'assassins-creed', 'type': 'game', 'description': 'سلسلة ألعاب أساسينز كريد'},
                'Grand Theft Auto': {'slug': 'grand-theft-auto', 'type': 'game', 'description': 'سلسلة ألعاب جراند ثفت أوتو'},
                'GTA': {'slug': 'grand-theft-auto', 'type': 'game', 'description': 'سلسلة ألعاب جراند ثفت أوتو'},
                'The Witcher': {'slug': 'the-witcher', 'type': 'game', 'description': 'سلسلة ألعاب ذا ويتشر'},
                'Cyberpunk 2077': {'slug': 'cyberpunk-2077', 'type': 'game', 'description': 'لعبة سايبربانك 2077'},
                'Fortnite': {'slug': 'fortnite', 'type': 'game', 'description': 'لعبة فورتنايت'},
                'Minecraft': {'slug': 'minecraft', 'type': 'game', 'description': 'لعبة ماينكرافت'},
                'Among Us': {'slug': 'among-us', 'type': 'game', 'description': 'لعبة أمونج أس'},
                'League of Legends': {'slug': 'league-of-legends', 'type': 'game', 'description': 'لعبة ليج أوف ليجندز'},
                'Valorant': {'slug': 'valorant', 'type': 'game', 'description': 'لعبة فالورانت'},
                'Overwatch': {'slug': 'overwatch', 'type': 'game', 'description': 'لعبة أوفرواتش'},
                'Apex Legends': {'slug': 'apex-legends', 'type': 'game', 'description': 'لعبة أبيكس ليجندز'},
                'Counter-Strike': {'slug': 'counter-strike', 'type': 'game', 'description': 'سلسلة ألعاب كاونتر سترايك'},
                'Dota 2': {'slug': 'dota-2', 'type': 'game', 'description': 'لعبة دوتا 2'},
                'World of Warcraft': {'slug': 'world-of-warcraft', 'type': 'game', 'description': 'لعبة وورلد أوف ووركرافت'},
                'Destiny': {'slug': 'destiny', 'type': 'game', 'description': 'سلسلة ألعاب ديستني'},
                'Red Dead Redemption': {'slug': 'red-dead-redemption', 'type': 'game', 'description': 'سلسلة ألعاب ريد ديد ريدمبشن'},
                'The Elder Scrolls': {'slug': 'the-elder-scrolls', 'type': 'game', 'description': 'سلسلة ألعاب ذا إلدر سكرولز'},
                'Fallout': {'slug': 'fallout', 'type': 'game', 'description': 'سلسلة ألعاب فولأوت'},
                'Final Fantasy': {'slug': 'final-fantasy', 'type': 'game', 'description': 'سلسلة ألعاب فاينال فانتازي'},
                'Resident Evil': {'slug': 'resident-evil', 'type': 'game', 'description': 'سلسلة ألعاب ريزيدنت إيفل'},
                'Street Fighter': {'slug': 'street-fighter', 'type': 'game', 'description': 'سلسلة ألعاب ستريت فايتر'},
                'Mortal Kombat': {'slug': 'mortal-kombat', 'type': 'game', 'description': 'سلسلة ألعاب مورتال كومبات'},
                'Super Mario': {'slug': 'super-mario', 'type': 'game', 'description': 'سلسلة ألعاب سوبر ماريو'},
                'The Legend of Zelda': {'slug': 'the-legend-of-zelda', 'type': 'game', 'description': 'سلسلة ألعاب ذا ليجند أوف زيلدا'},
                'Pokémon': {'slug': 'pokemon', 'type': 'game', 'description': 'سلسلة ألعاب بوكيمون'},
                'Halo': {'slug': 'halo', 'type': 'game', 'description': 'سلسلة ألعاب هالو'},
                'Gears of War': {'slug': 'gears-of-war', 'type': 'game', 'description': 'سلسلة ألعاب جيرز أوف وور'},
                'God of War': {'slug': 'god-of-war', 'type': 'game', 'description': 'سلسلة ألعاب جود أوف وور'}
            }
            
            # الأشخاص المهمين في عالم الألعاب
            self.persons_db = {
                'Hideo Kojima': {'slug': 'hideo-kojima', 'type': 'person', 'description': 'مطور الألعاب الياباني الشهير'},
                'Shigeru Miyamoto': {'slug': 'shigeru-miyamoto', 'type': 'person', 'description': 'مطور ألعاب نينتندو الأسطوري'},
                'Todd Howard': {'slug': 'todd-howard', 'type': 'person', 'description': 'مدير بيثيسدا جيم ستوديوز'},
                'Gabe Newell': {'slug': 'gabe-newell', 'type': 'person', 'description': 'مؤسس شركة فالف'},
                'Tim Sweeney': {'slug': 'tim-sweeney', 'type': 'person', 'description': 'مؤسس إيبك جيمز'},
                'John Carmack': {'slug': 'john-carmack', 'type': 'person', 'description': 'مطور الألعاب الأسطوري'},
                'Cliff Bleszinski': {'slug': 'cliff-bleszinski', 'type': 'person', 'description': 'مطور ألعاب مشهور'},
                'Will Wright': {'slug': 'will-wright', 'type': 'person', 'description': 'مطور ألعاب المحاكاة'},
                'Sid Meier': {'slug': 'sid-meier', 'type': 'person', 'description': 'مطور ألعاب الاستراتيجية'},
                'Peter Molyneux': {'slug': 'peter-molyneux', 'type': 'person', 'description': 'مطور الألعاب البريطاني'}
            }
            
            logger.info("✅ تم تحميل قواعد بيانات الكيانات")
            
        except Exception as e:
            logger.error("❌ فشل في تحميل قواعد بيانات الكيانات", e)
    
    def add_internal_links_to_content(self, content: str, article_id: Optional[int] = None) -> Tuple[str, Dict]:
        """إضافة الروابط الداخلية للمحتوى"""
        try:
            enhanced_content = content
            links_added = {
                'companies': [],
                'games': [],
                'platforms': [],
                'persons': [],
                'total_links': 0
            }
            
            # إضافة روابط الشركات
            for entity_name, entity_data in self.companies_db.items():
                enhanced_content, added = self._add_entity_link(
                    enhanced_content, entity_name, entity_data, 'company'
                )
                if added:
                    links_added['companies'].append(entity_name)
                    links_added['total_links'] += 1
            
            # إضافة روابط الألعاب
            for entity_name, entity_data in self.games_db.items():
                enhanced_content, added = self._add_entity_link(
                    enhanced_content, entity_name, entity_data, 'game'
                )
                if added:
                    links_added['games'].append(entity_name)
                    links_added['total_links'] += 1
            
            # إضافة روابط المنصات
            for entity_name, entity_data in self.platforms_db.items():
                enhanced_content, added = self._add_entity_link(
                    enhanced_content, entity_name, entity_data, 'platform'
                )
                if added:
                    links_added['platforms'].append(entity_name)
                    links_added['total_links'] += 1
            
            # إضافة روابط الأشخاص
            for entity_name, entity_data in self.persons_db.items():
                enhanced_content, added = self._add_entity_link(
                    enhanced_content, entity_name, entity_data, 'person'
                )
                if added:
                    links_added['persons'].append(entity_name)
                    links_added['total_links'] += 1
            
            # حفظ إحصائيات الروابط إذا كان معرف المقال متوفر
            if article_id:
                self._save_link_statistics(article_id, links_added)
            
            logger.info(f"✅ تم إضافة {links_added['total_links']} رابط داخلي للمحتوى")
            
            return enhanced_content, links_added
            
        except Exception as e:
            logger.error("❌ فشل في إضافة الروابط الداخلية", e)
            return content, {'total_links': 0}
    
    def _add_entity_link(self, content: str, entity_name: str, entity_data: Dict, entity_type: str) -> Tuple[str, bool]:
        """إضافة رابط لكيان واحد"""
        try:
            # فحص ما إذا كان الكيان موجود في المحتوى
            pattern = r'\b' + re.escape(entity_name) + r'\b'

            # فحص ما إذا كان الرابط موجود بالفعل
            link_pattern = f'href="'
            if re.search(link_pattern + r'[^"]*' + re.escape(entity_name.lower().replace(' ', '-')) + r'"', content):
                return content, False

            # البحث عن أول ذكر للكيان وإضافة رابط له
            match = re.search(pattern, content)
            if match:
                # البحث عن مقال حقيقي مرتبط بهذا الكيان
                real_article_url = self._find_real_article_for_entity(entity_name, entity_type)

                if real_article_url:
                    # استخدام رابط المقال الحقيقي أو الرابط الرسمي
                    linked_text = f'<a href="{real_article_url}" target="_blank">{entity_name}</a>'
                else:
                    # استخدام رابط متخصص بدلاً من رابط فارغ
                    specialized_url = self._get_specialized_search_url(entity_name, entity_type)
                    linked_text = f'<a href="{specialized_url}" target="_blank" rel="noopener">{entity_name}</a>'

                # استبدال أول ذكر فقط
                enhanced_content = re.sub(pattern, linked_text, content, count=1)
                return enhanced_content, True

            return content, False

        except Exception as e:
            logger.warning(f"⚠️ خطأ في إضافة رابط {entity_name}: {e}")
            return content, False
    
    def _save_link_statistics(self, article_id: int, links_data: Dict):
        """حفظ إحصائيات الروابط"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO link_statistics
                    (article_id, total_internal_links, unique_entities_linked,
                     companies_linked, games_linked, platforms_linked, persons_linked,
                     link_density, analysis_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    article_id,
                    links_data['total_links'],
                    len(set(links_data['companies'] + links_data['games'] + 
                           links_data['platforms'] + links_data['persons'])),
                    len(links_data['companies']),
                    len(links_data['games']),
                    len(links_data['platforms']),
                    len(links_data['persons']),
                    0.0,  # سيتم حسابها لاحقاً
                    datetime.now().date()
                ))
                
                conn.commit()
                
        except Exception as e:
            logger.error("❌ فشل في حفظ إحصائيات الروابط", e)

    def _find_real_article_for_entity(self, entity_name: str, entity_type: str) -> Optional[str]:
        """البحث عن مقال حقيقي مرتبط بالكيان أو رابط رسمي"""
        try:
            # أولاً: البحث عن رابط رسمي محدد مسبقاً
            official_url = self._get_official_entity_url(entity_name, entity_type)
            if official_url:
                logger.info(f"🔗 تم العثور على رابط رسمي لـ {entity_name}: {official_url}")
                return official_url

            # ثانياً: البحث في المقالات المنشورة
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # البحث في المقالات المنشورة عن مقالات تحتوي على اسم الكيان
                search_patterns = [
                    f"%{entity_name}%",
                    f"%{entity_name.lower()}%",
                    f"%{entity_name.replace(' ', '%')}%"
                ]

                for pattern in search_patterns:
                    cursor.execute('''
                        SELECT blogger_url, title
                        FROM published_articles
                        WHERE (title LIKE ? OR content LIKE ?)
                        AND blogger_url IS NOT NULL
                        AND blogger_url != ''
                        ORDER BY published_at DESC
                        LIMIT 1
                    ''', (pattern, pattern))

                    result = cursor.fetchone()
                    if result:
                        blogger_url, title = result
                        logger.info(f"🔗 تم العثور على مقال حقيقي لـ {entity_name}: {title}")
                        return blogger_url

                # إذا لم نجد مقال، ابحث عن مقالات في نفس التصنيف
                category_keywords = self._get_category_keywords(entity_type)
                for keyword in category_keywords:
                    cursor.execute('''
                        SELECT blogger_url, title
                        FROM published_articles
                        WHERE (title LIKE ? OR content LIKE ?)
                        AND blogger_url IS NOT NULL
                        AND blogger_url != ''
                        ORDER BY published_at DESC
                        LIMIT 1
                    ''', (f"%{keyword}%", f"%{keyword}%"))

                    result = cursor.fetchone()
                    if result:
                        blogger_url, title = result
                        logger.info(f"🔗 تم العثور على مقال ذو صلة لـ {entity_name}: {title}")
                        return blogger_url

                return None

        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن مقال حقيقي لـ {entity_name}: {e}")
            return None

    def _get_official_entity_url(self, entity_name: str, entity_type: str) -> Optional[str]:
        """الحصول على الرابط الرسمي للكيان"""
        try:
            entity_lower = entity_name.lower()

            # قاعدة بيانات الروابط الرسمية للشركات
            if entity_type == 'company':
                company_urls = {
                    'sony': 'https://www.playstation.com',
                    'microsoft': 'https://www.xbox.com',
                    'nintendo': 'https://www.nintendo.com',
                    'valve': 'https://store.steampowered.com',
                    'epic games': 'https://www.epicgames.com',
                    'activision': 'https://www.activision.com',
                    'blizzard': 'https://www.blizzard.com',
                    'ubisoft': 'https://www.ubisoft.com',
                    'ea': 'https://www.ea.com',
                    'electronic arts': 'https://www.ea.com',
                    'rockstar': 'https://www.rockstargames.com',
                    'cd projekt': 'https://www.cdprojekt.com',
                    'bethesda': 'https://bethesda.net',
                    'square enix': 'https://www.square-enix.com',
                    'capcom': 'https://www.capcom.com',
                    'konami': 'https://www.konami.com',
                    'bandai namco': 'https://www.bandainamcoent.com',
                    'sega': 'https://www.sega.com',
                    'take-two': 'https://www.take2games.com',
                    'riot games': 'https://www.riotgames.com',
                    'bungie': 'https://www.bungie.net',
                    'respawn': 'https://www.respawn.com',
                    'naughty dog': 'https://www.naughtydog.com',
                    'insomniac': 'https://insomniacgames.com',
                    'guerrilla games': 'https://www.guerrilla-games.com',
                    'santa monica studio': 'https://sms.playstation.com',
                    'fromsoft': 'https://www.fromsoftware.jp',
                    'fromsoftware': 'https://www.fromsoftware.jp',
                    'mihoyo': 'https://www.mihoyo.com',
                    'hoyoverse': 'https://www.hoyoverse.com'
                }

                for company, url in company_urls.items():
                    if company in entity_lower:
                        return url

            # قاعدة بيانات الروابط الرسمية للألعاب الشهيرة
            elif entity_type == 'game':
                game_urls = {
                    'fortnite': 'https://www.epicgames.com/fortnite',
                    'minecraft': 'https://www.minecraft.net',
                    'call of duty': 'https://www.callofduty.com',
                    'fifa': 'https://www.ea.com/fifa',
                    'gta': 'https://www.rockstargames.com/gta',
                    'grand theft auto': 'https://www.rockstargames.com/gta',
                    'cyberpunk': 'https://www.cyberpunk.net',
                    'witcher': 'https://thewitcher.com',
                    'overwatch': 'https://overwatch.blizzard.com',
                    'league of legends': 'https://www.leagueoflegends.com',
                    'valorant': 'https://playvalorant.com',
                    'apex legends': 'https://www.ea.com/games/apex-legends',
                    'destiny': 'https://www.bungie.net/7/en/Destiny',
                    'halo': 'https://www.halowaypoint.com',
                    'god of war': 'https://www.playstation.com/games/god-of-war',
                    'spider-man': 'https://insomniacgames.com/games',
                    'horizon': 'https://www.guerrilla-games.com/games',
                    'the last of us': 'https://www.naughtydog.com/games',
                    'uncharted': 'https://www.naughtydog.com/games',
                    'zelda': 'https://www.nintendo.com/games/zelda',
                    'mario': 'https://www.nintendo.com/games/mario',
                    'pokemon': 'https://www.pokemon.com',
                    'genshin impact': 'https://genshin.hoyoverse.com',
                    'honkai': 'https://honkaiimpact3.hoyoverse.com',
                    'elden ring': 'https://en.bandainamcoent.eu/elden-ring',
                    'dark souls': 'https://www.fromsoftware.jp',
                    'sekiro': 'https://www.sekirothegame.com',
                    'bloodborne': 'https://www.playstation.com/games/bloodborne'
                }

                for game, url in game_urls.items():
                    if game in entity_lower:
                        return url

            # قاعدة بيانات الروابط للمنصات
            elif entity_type == 'platform':
                platform_urls = {
                    'steam': 'https://store.steampowered.com',
                    'playstation': 'https://www.playstation.com',
                    'xbox': 'https://www.xbox.com',
                    'nintendo switch': 'https://www.nintendo.com/switch',
                    'epic games store': 'https://store.epicgames.com',
                    'origin': 'https://www.origin.com',
                    'uplay': 'https://uplay.ubisoft.com',
                    'battle.net': 'https://www.blizzard.com/apps/battle.net',
                    'gog': 'https://www.gog.com',
                    'itch.io': 'https://itch.io'
                }

                for platform, url in platform_urls.items():
                    if platform in entity_lower:
                        return url

            return None

        except Exception as e:
            logger.warning(f"⚠️ خطأ في الحصول على الرابط الرسمي للكيان {entity_name}: {e}")
            return None

    def _get_specialized_search_url(self, entity_name: str, entity_type: str) -> str:
        """الحصول على رابط بحث متخصص حسب نوع الكيان"""
        try:
            entity_lower = entity_name.lower()
            search_query = entity_name.replace(' ', '+')

            # روابط متخصصة حسب نوع الكيان
            if entity_type == 'company':
                # للشركات: البحث في مواقع الألعاب المتخصصة
                return f"https://www.gamespot.com/search/?q={search_query}"

            elif entity_type == 'game':
                # للألعاب: البحث في Steam أو Metacritic
                if any(keyword in entity_lower for keyword in ['mobile', 'android', 'ios']):
                    return f"https://play.google.com/store/search?q={search_query}&c=apps&hl=en"
                else:
                    return f"https://store.steampowered.com/search/?term={search_query}"

            elif entity_type == 'platform':
                # للمنصات: روابط مباشرة للمنصات
                if 'steam' in entity_lower:
                    return "https://store.steampowered.com"
                elif 'playstation' in entity_lower or 'ps' in entity_lower:
                    return "https://www.playstation.com"
                elif 'xbox' in entity_lower:
                    return "https://www.xbox.com"
                elif 'nintendo' in entity_lower:
                    return "https://www.nintendo.com"
                else:
                    return f"https://www.ign.com/search?q={search_query}"

            elif entity_type == 'person':
                # للأشخاص: البحث في مواقع الأخبار المتخصصة
                return f"https://www.gamesindustry.biz/search?q={search_query}"

            else:
                # افتراضي: بحث في IGN
                return f"https://www.ign.com/search?q={search_query}"

        except Exception as e:
            logger.warning(f"⚠️ خطأ في إنشاء رابط البحث المتخصص: {e}")
            # رابط احتياطي
            search_query = entity_name.replace(' ', '+')
            return f"https://www.google.com/search?q={search_query}+gaming+news"

    def _get_category_keywords(self, entity_type: str) -> List[str]:
        """الحصول على كلمات مفتاحية حسب نوع الكيان"""
        keywords_map = {
            'company': ['gaming', 'game', 'developer', 'publisher'],
            'game': ['game', 'gaming', 'video game', 'gameplay'],
            'platform': ['console', 'gaming', 'platform', 'system'],
            'person': ['gaming', 'developer', 'game', 'industry']
        }

        return keywords_map.get(entity_type, ['gaming', 'game'])

    def update_entity_article_mapping(self, entity_name: str, entity_type: str, article_url: str):
        """تحديث ربط الكيان بمقال معين"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # البحث عن الكيان
                cursor.execute('''
                    SELECT id FROM entities
                    WHERE name = ? AND type = ?
                ''', (entity_name, entity_type))

                entity_result = cursor.fetchone()
                if entity_result:
                    entity_id = entity_result[0]

                    # البحث عن المقال
                    cursor.execute('''
                        SELECT id FROM published_articles
                        WHERE blogger_url = ?
                    ''', (article_url,))

                    article_result = cursor.fetchone()
                    if article_result:
                        article_id = article_result[0]

                        # تحديث الكيان بمعرف المقال
                        cursor.execute('''
                            UPDATE entities
                            SET first_mentioned_article_id = ?, updated_at = CURRENT_TIMESTAMP
                            WHERE id = ?
                        ''', (article_id, entity_id))

                        conn.commit()
                        logger.info(f"✅ تم ربط {entity_name} بالمقال {article_url}")

        except Exception as e:
            logger.error(f"❌ خطأ في تحديث ربط الكيان: {e}")

# إنشاء كائن مدير الروابط الداخلية
internal_links_manager = InternalLinksManager()
