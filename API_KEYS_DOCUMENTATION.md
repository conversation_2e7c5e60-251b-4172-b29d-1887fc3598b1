# 🔑 دليل مفاتيح API الشامل

## 📋 نظرة عامة

هذا الدليل يحتوي على جميع مفاتيح API المطلوبة لتشغيل الوكيل بكامل إمكانياته. تم تنظيم المفاتيح حسب الفئات والخدمات.

## 🔍 مفاتيح البحث والاستخراج

### Google Enhanced API Keys (14 مفتاح)
- **الحالة**: معطلة حالياً
- **الاستخدام**: بحث محسن عبر Google
- **المتغيرات**: `GOOGLE_ENHANCED_API_KEY_1` إلى `GOOGLE_ENHANCED_API_KEY_14`

### Google Search API Keys (10 مفاتيح)
- **الحالة**: تم تعطيلها لتجنب الحظر
- **البديل**: نظام البحث الذكي البديل
- **المتغيرات**: `GOOGLE_SEARCH_API_KEY_1` إلى `GOOGLE_SEARCH_API_KEY_10`

### SerpAPI Keys
- **الحالة**: يعمل (مفتاح واحد فعال)
- **الاستخدام**: بحث متقدم عبر محركات البحث
- **المتغيرات**: `SERPAPI_KEY_1`, `SERPAPI_KEY_2`, `SERPAPI_KEY_3`

### Tavily API Keys
- **الحالة**: غير مضبوط
- **الاستخدام**: بحث ذكي للأخبار
- **المتغيرات**: `TAVILY_API_KEY_1`, `TAVILY_API_KEY_2`

### Search1API Keys
- **الحالة**: يعمل (3 مفاتيح)
- **الاستخدام**: بحث بديل
- **المتغيرات**: `SEARCH1API_KEY_1`, `SEARCH1API_KEY_2`, `SEARCH1API_KEY_3`

## 🤖 مفاتيح الذكاء الاصطناعي

### Gemini API
- **الحالة**: يعمل
- **الاستخدام**: توليد المحتوى الرئيسي
- **المتغير**: `GEMINI_API_KEY`

### OpenAI API Keys
- **الحالة**: غير مضبوط
- **الاستخدام**: GPT-4, DALL-E, Whisper
- **المتغيرات**: `OPENAI_API_KEY_1`, `OPENAI_API_KEY_2`, `OPENAI_API_KEY_3`

### Claude API Keys
- **الحالة**: غير مضبوط
- **الاستخدام**: توليد محتوى بديل
- **المتغيرات**: `CLAUDE_API_KEY_1`, `CLAUDE_API_KEY_2`

## 🎤 مفاتيح تحويل الصوت إلى نص

### AssemblyAI
- **الحالة**: غير مضبوط
- **الاستخدام**: تحويل صوت فيديوهات YouTube
- **المتغيرات**: `ASSEMBLYAI_API_KEY_1`, `ASSEMBLYAI_API_KEY_2`

### Speechmatics
- **الحالة**: غير مضبوط
- **الاستخدام**: تحويل صوت متقدم
- **المتغيرات**: `SPEECHMATICS_API_KEY_1`, `SPEECHMATICS_API_KEY_2`

### IBM Watson Speech to Text
- **الحالة**: غير مضبوط
- **الاستخدام**: تحويل صوت احترافي
- **المتغيرات**: `IBM_WATSON_API_KEY`, `IBM_WATSON_URL`

### Azure Speech Services
- **الحالة**: غير مضبوط
- **الاستخدام**: تحويل صوت Microsoft
- **المتغيرات**: `AZURE_SPEECH_KEY_1`, `AZURE_SPEECH_KEY_2`, `AZURE_SPEECH_REGION`

### Google Cloud Speech-to-Text
- **الحالة**: غير مضبوط
- **الاستخدام**: تحويل صوت Google Cloud
- **المتغيرات**: `GOOGLE_CLOUD_SPEECH_KEY_1`, `GOOGLE_CLOUD_SPEECH_KEY_2`

### Wit.ai
- **الحالة**: غير مضبوط
- **الاستخدام**: تحويل صوت Facebook
- **المتغيرات**: `WIT_AI_ACCESS_TOKEN_1`, `WIT_AI_ACCESS_TOKEN_2`

### Whisper API
- **الحالة**: غير مضبوط
- **الاستخدام**: تحويل صوت OpenAI
- **المتغيرات**: `WHISPER_API_KEY_1`, `WHISPER_API_KEY_2`

## 📹 مفاتيح تحميل الفيديو والصوت

### Apify YouTube Downloader
- **الحالة**: مضبوط جزئياً
- **الاستخدام**: تحميل فيديوهات YouTube
- **المتغير**: `APIFY_API_TOKEN`

### YouTube Data API
- **الحالة**: غير مضبوط
- **الاستخدام**: بيانات فيديوهات YouTube
- **المتغيرات**: `YOUTUBE_DATA_API_KEY_1`, `YOUTUBE_DATA_API_KEY_2`, `YOUTUBE_DATA_API_KEY_3`

### RapidAPI Keys
- **الحالة**: غير مضبوط
- **الاستخدام**: خدمات فيديو متنوعة
- **المتغيرات**: `RAPIDAPI_KEY_1`, `RAPIDAPI_KEY_2`, `RAPIDAPI_KEY_3`

## 🖼️ مفاتيح الصور والوسائط

### RAWG API
- **الحالة**: يعمل
- **الاستخدام**: بيانات الألعاب
- **المتغير**: `RAWG_API_KEY`

### IGDB API
- **الحالة**: غير مضبوط
- **الاستخدام**: قاعدة بيانات الألعاب
- **المتغيرات**: `IGDB_CLIENT_ID`, `IGDB_CLIENT_SECRET`

### Giant Bomb API
- **الحالة**: غير مضبوط
- **الاستخدام**: أخبار ومراجعات الألعاب
- **المتغيرات**: `GIANT_BOMB_API_KEY_1`, `GIANT_BOMB_API_KEY_2`

### MobyGames API
- **الحالة**: غير مضبوط
- **الاستخدام**: تاريخ الألعاب
- **المتغيرات**: `MOBYGAMES_API_KEY_1`, `MOBYGAMES_API_KEY_2`

### Unsplash API
- **الحالة**: غير مضبوط
- **الاستخدام**: صور عالية الجودة
- **المتغيرات**: `UNSPLASH_ACCESS_KEY_1`, `UNSPLASH_ACCESS_KEY_2`

### Pexels API
- **الحالة**: غير مضبوط
- **الاستخدام**: صور مجانية
- **المتغيرات**: `PEXELS_API_KEY_1`, `PEXELS_API_KEY_2`, `PEXELS_API_KEY_3`

### Pixabay API
- **الحالة**: غير مضبوط
- **الاستخدام**: صور ورسوم مجانية
- **المتغيرات**: `PIXABAY_API_KEY_1`, `PIXABAY_API_KEY_2`, `PIXABAY_API_KEY_3`

### Steam API
- **الحالة**: غير مضبوط
- **الاستخدام**: بيانات Steam
- **المتغيرات**: `STEAM_API_KEY_1`, `STEAM_API_KEY_2`

## 🎨 مفاتيح توليد الصور بالذكاء الاصطناعي

### Stability AI
- **الحالة**: غير مضبوط
- **الاستخدام**: Stable Diffusion
- **المتغيرات**: `STABILITY_AI_API_KEY_1`, `STABILITY_AI_API_KEY_2`

### Midjourney API
- **الحالة**: غير مضبوط
- **الاستخدام**: توليد صور فنية
- **المتغيرات**: `MIDJOURNEY_API_KEY_1`, `MIDJOURNEY_API_KEY_2`

### DALL-E API
- **الحالة**: غير مضبوط
- **الاستخدام**: توليد صور OpenAI
- **المتغيرات**: `DALLE_API_KEY_1`, `DALLE_API_KEY_2`

### Leap AI
- **الحالة**: يعمل
- **الاستخدام**: توليد صور
- **المتغير**: `LEAP_AI_API_KEY`

### DeepAI
- **الحالة**: يعمل
- **الاستخدام**: توليد صور
- **المتغير**: `DEEPAI_API_KEY`

### Replicate
- **الحالة**: يعمل
- **الاستخدام**: نماذج AI متنوعة
- **المتغير**: `REPLICATE_API_KEY`

### Freepik API
- **الحالة**: يعمل
- **الاستخدام**: صور وأيقونات
- **المتغير**: `FREEPIK_API_KEY`

### FluxAI
- **الحالة**: يعمل
- **الاستخدام**: توليد صور
- **المتغير**: `FLUXAI_API_KEY`

## 📝 مفاتيح النشر والمدونات

### Blogger API
- **الحالة**: يعمل
- **الاستخدام**: النشر على Blogger
- **المتغيرات**: `BLOGGER_CLIENT_ID`, `BLOGGER_CLIENT_SECRET`, `BLOGGER_BLOG_ID`

### WordPress API
- **الحالة**: غير مضبوط
- **الاستخدام**: النشر على WordPress
- **المتغيرات**: `WORDPRESS_API_KEY_1`, `WORDPRESS_API_KEY_2`

### Medium API
- **الحالة**: غير مضبوط
- **الاستخدام**: النشر على Medium
- **المتغيرات**: `MEDIUM_ACCESS_TOKEN_1`, `MEDIUM_ACCESS_TOKEN_2`

### Ghost API
- **الحالة**: غير مضبوط
- **الاستخدام**: النشر على Ghost
- **المتغيرات**: `GHOST_ADMIN_API_KEY`, `GHOST_CONTENT_API_KEY`

## 📱 مفاتيح وسائل التواصل الاجتماعي

### Telegram Bot
- **الحالة**: يعمل
- **الاستخدام**: النشر على Telegram
- **المتغيرات**: `TELEGRAM_BOT_TOKEN`, `TELEGRAM_CHANNEL_ID`

### Twitter/X API
- **الحالة**: غير مضبوط
- **الاستخدام**: النشر على Twitter
- **المتغيرات**: `TWITTER_API_KEY_1`, `TWITTER_ACCESS_TOKEN_1`

### Facebook API
- **الحالة**: غير مضبوط
- **الاستخدام**: النشر على Facebook
- **المتغيرات**: `FACEBOOK_ACCESS_TOKEN_1`, `FACEBOOK_PAGE_ID`

### Instagram API
- **الحالة**: غير مضبوط
- **الاستخدام**: النشر على Instagram
- **المتغيرات**: `INSTAGRAM_ACCESS_TOKEN_1`, `INSTAGRAM_BUSINESS_ACCOUNT_ID`

### Discord Webhook
- **الحالة**: غير مضبوط
- **الاستخدام**: إشعارات Discord
- **المتغيرات**: `DISCORD_WEBHOOK_URL_1`, `DISCORD_WEBHOOK_URL_2`

## 📊 مفاتيح التحليلات والمراقبة

### Google Analytics
- **الحالة**: غير مضبوط
- **الاستخدام**: تحليل الزوار
- **المتغيرات**: `GOOGLE_ANALYTICS_TRACKING_ID`, `GOOGLE_ANALYTICS_API_KEY`

### SEMrush API
- **الحالة**: غير مضبوط
- **الاستخدام**: تحليل SEO
- **المتغيرات**: `SEMRUSH_API_KEY_1`, `SEMRUSH_API_KEY_2`

### Ahrefs API
- **الحالة**: غير مضبوط
- **الاستخدام**: تحليل الروابط
- **المتغيرات**: `AHREFS_API_KEY_1`, `AHREFS_API_KEY_2`

## 🌐 مفاتيح الترجمة

### Google Translate API
- **الحالة**: غير مضبوط
- **الاستخدام**: ترجمة المحتوى
- **المتغيرات**: `GOOGLE_TRANSLATE_API_KEY_1`, `GOOGLE_TRANSLATE_API_KEY_2`

### DeepL API
- **الحالة**: غير مضبوط
- **الاستخدام**: ترجمة عالية الجودة
- **المتغيرات**: `DEEPL_API_KEY_1`, `DEEPL_API_KEY_2`

### Microsoft Translator
- **الحالة**: غير مضبوط
- **الاستخدام**: ترجمة Microsoft
- **المتغيرات**: `MICROSOFT_TRANSLATOR_KEY_1`, `MICROSOFT_TRANSLATOR_KEY_2`

## 📰 مفاتيح الأخبار

### NewsAPI
- **الحالة**: غير مضبوط
- **الاستخدام**: أخبار عالمية
- **المتغيرات**: `NEWS_API_KEY_1`, `NEWS_API_KEY_2`, `NEWS_API_KEY_3`

### Reddit API
- **الحالة**: غير مضبوط
- **الاستخدام**: محتوى Reddit
- **المتغيرات**: `REDDIT_CLIENT_ID`, `REDDIT_CLIENT_SECRET`

## ☁️ مفاتيح التخزين السحابي

### AWS S3
- **الحالة**: غير مضبوط
- **الاستخدام**: تخزين الملفات
- **المتغيرات**: `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`

### Google Cloud Storage
- **الحالة**: غير مضبوط
- **الاستخدام**: تخزين Google Cloud
- **المتغيرات**: `GOOGLE_CLOUD_STORAGE_KEY`, `GOOGLE_CLOUD_PROJECT_ID`

### Cloudinary
- **الحالة**: غير مضبوط
- **الاستخدام**: إدارة الصور
- **المتغيرات**: `CLOUDINARY_CLOUD_NAME`, `CLOUDINARY_API_KEY`

## 🗄️ مفاتيح قواعد البيانات

### MongoDB Atlas
- **الحالة**: غير مضبوط
- **الاستخدام**: قاعدة بيانات NoSQL
- **المتغير**: `MONGODB_CONNECTION_STRING`

### Firebase
- **الحالة**: غير مضبوط
- **الاستخدام**: قاعدة بيانات Google
- **المتغيرات**: `FIREBASE_API_KEY`, `FIREBASE_PROJECT_ID`

### Supabase
- **الحالة**: غير مضبوط
- **الاستخدام**: قاعدة بيانات مفتوحة المصدر
- **المتغيرات**: `SUPABASE_URL`, `SUPABASE_ANON_KEY`

## 🔔 مفاتيح الإشعارات

### Pushover
- **الحالة**: غير مضبوط
- **الاستخدام**: إشعارات الهاتف
- **المتغيرات**: `PUSHOVER_USER_KEY`, `PUSHOVER_API_TOKEN`

### Slack Webhook
- **الحالة**: غير مضبوط
- **الاستخدام**: إشعارات Slack
- **المتغير**: `SLACK_WEBHOOK_URL`

### SendGrid
- **الحالة**: غير مضبوط
- **الاستخدام**: إرسال الإيميلات
- **المتغيرات**: `SENDGRID_API_KEY`, `SENDGRID_FROM_EMAIL`

## 🔧 مفاتيح خدمات أخرى

### Brave Search
- **الحالة**: غير مضبوط
- **الاستخدام**: بحث Brave
- **المتغير**: `BRAVE_SEARCH_KEY`

### Bing Search API
- **الحالة**: غير مضبوط
- **الاستخدام**: بحث Bing
- **المتغيرات**: `BING_SEARCH_API_KEY_1`, `BING_SEARCH_API_KEY_2`

### Yandex Search API
- **الحالة**: غير مضبوط
- **الاستخدام**: بحث Yandex
- **المتغيرات**: `YANDEX_SEARCH_API_KEY_1`, `YANDEX_SEARCH_API_KEY_2`

### ScraperAPI
- **الحالة**: غير مضبوط
- **الاستخدام**: استخراج المحتوى
- **المتغيرات**: `SCRAPERAPI_KEY_1`, `SCRAPERAPI_KEY_2`

### ProxyMesh
- **الحالة**: غير مضبوط
- **الاستخدام**: خدمات البروكسي
- **المتغيرات**: `PROXYMESH_USERNAME`, `PROXYMESH_PASSWORD`

## ⚙️ الإعدادات العامة

### إعدادات التطوير
- `DEBUG_MODE=false`
- `LOG_LEVEL=INFO`
- `MAX_CONCURRENT_REQUESTS=5`
- `REQUEST_TIMEOUT_SECONDS=30`

### إعدادات التخزين المؤقت
- `CACHE_ENABLED=true`
- `CACHE_TTL_HOURS=24`
- `CACHE_MAX_SIZE_MB=500`

### إعدادات الأمان
- `RATE_LIMIT_ENABLED=true`
- `MAX_REQUESTS_PER_MINUTE=60`
- `ENABLE_API_KEY_ROTATION=true`

## 📝 ملاحظات مهمة

1. **الأولوية**: ركز على الخدمات التي تعمل حالياً وأضف المفاتيح الجديدة تدريجياً
2. **الأمان**: لا تشارك المفاتيح الحقيقية في الكود المصدري
3. **التكلفة**: راقب استخدام الخدمات المدفوعة
4. **البدائل**: استخدم الخدمات المجانية كبدائل عند الإمكان
5. **التحديث**: حدث المفاتيح المنتهية الصلاحية بانتظام

## 🚀 الخطوات التالية

1. **إضافة مفاتيح جديدة** للخدمات المطلوبة
2. **اختبار الخدمات** للتأكد من عملها
3. **تحسين إدارة المفاتيح** مع نظام التدوير التلقائي
4. **مراقبة الاستخدام** وتحسين الأداء
5. **إضافة خدمات جديدة** حسب الحاجة
