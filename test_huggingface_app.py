# -*- coding: utf-8 -*-
"""
اختبار سريع للواجهة الإدارية على Hugging Face
"""

import sys
import os
import asyncio
from datetime import datetime

# إضافة المسار للوحدات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار استيراد الوحدات المطلوبة"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        import gradio as gr
        print("✅ Gradio متوفر")
    except ImportError:
        print("❌ Gradio غير متوفر - قم بتثبيته: pip install gradio")
        return False
    
    try:
        from huggingface_config import hf_config
        print("✅ تكوين Hugging Face متوفر")
    except ImportError:
        print("❌ تكوين Hugging Face غير متوفر")
        return False
    
    try:
        from config.new_image_apis_config import new_image_apis_config
        print("✅ تكوين APIs الصور متوفر")
    except ImportError:
        print("⚠️ تكوين APIs الصور غير متوفر (سيتم استخدام البيانات التجريبية)")
    
    return True

def test_directories():
    """اختبار إنشاء المجلدات"""
    print("\n📁 اختبار المجلدات...")
    
    from huggingface_config import hf_config
    
    # إنشاء المجلدات
    hf_config.create_directories()
    
    # فحص المجلدات
    directories = ['logs', 'cache', 'data', 'cache/images']
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"✅ {directory}")
        else:
            print(f"❌ {directory}")
    
    return True

def test_sample_data():
    """اختبار إنشاء البيانات التجريبية"""
    print("\n📊 اختبار البيانات التجريبية...")
    
    from huggingface_config import hf_config
    
    # إنشاء البيانات التجريبية
    hf_config.create_sample_data()
    
    # فحص قاعدة البيانات
    db_file = "data/articles.db"
    if os.path.exists(db_file):
        print("✅ قاعدة البيانات التجريبية")
        
        # فحص المحتوى
        import sqlite3
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM articles")
        count = cursor.fetchone()[0]
        print(f"📰 عدد المقالات التجريبية: {count}")
        conn.close()
    else:
        print("❌ قاعدة البيانات التجريبية")
    
    # فحص ملف السجل
    log_file = "logs/bot.log"
    if os.path.exists(log_file):
        print("✅ ملف السجل التجريبي")
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            print(f"📝 عدد أسطر السجل: {len(lines)}")
    else:
        print("❌ ملف السجل التجريبي")
    
    return True

def test_gradio_interface():
    """اختبار واجهة Gradio"""
    print("\n🎨 اختبار واجهة Gradio...")
    
    try:
        import gradio as gr
        
        # إنشاء واجهة بسيطة للاختبار
        def test_function(text):
            return f"تم استلام: {text}"
        
        with gr.Blocks() as demo:
            gr.Markdown("# اختبار واجهة Gradio")
            input_text = gr.Textbox(label="أدخل نص")
            output_text = gr.Textbox(label="النتيجة")
            test_btn = gr.Button("اختبار")
            
            test_btn.click(test_function, inputs=input_text, outputs=output_text)
        
        print("✅ تم إنشاء واجهة Gradio بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء واجهة Gradio: {e}")
        return False

def test_environment():
    """اختبار متغيرات البيئة"""
    print("\n🌍 اختبار البيئة...")
    
    from huggingface_config import hf_config
    
    # إعداد البيئة
    hf_config.setup_environment()
    
    # فحص متغيرات البيئة المهمة
    env_vars = [
        'GRADIO_SERVER_NAME',
        'GRADIO_SERVER_PORT',
        'PYTHONPATH'
    ]
    
    for var in env_vars:
        value = os.environ.get(var, 'غير محدد')
        print(f"🔧 {var}: {value}")
    
    # فحص ما إذا كان يعمل على Hugging Face
    if hf_config.is_huggingface_space():
        print("🚀 يعمل على Hugging Face Spaces")
        space_info = hf_config.get_space_info()
        if space_info:
            print(f"📍 Space ID: {space_info['space_id']}")
    else:
        print("💻 يعمل محلياً")
    
    return True

def test_app_launch():
    """اختبار تشغيل التطبيق"""
    print("\n🚀 اختبار تشغيل التطبيق...")
    
    try:
        # استيراد التطبيق
        from app import create_admin_interface
        
        # إنشاء الواجهة
        interface = create_admin_interface()
        print("✅ تم إنشاء واجهة الإدارة بنجاح")
        
        # اختبار التشغيل (بدون launch فعلي)
        print("✅ التطبيق جاهز للتشغيل")
        print("💡 لتشغيل التطبيق: python app.py")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🎮 اختبار شامل لواجهة Hugging Face")
    print("=" * 50)
    
    tests = [
        ("استيراد الوحدات", test_imports),
        ("المجلدات", test_directories),
        ("البيانات التجريبية", test_sample_data),
        ("واجهة Gradio", test_gradio_interface),
        ("البيئة", test_environment),
        ("تشغيل التطبيق", test_app_launch)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 اختبار {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 النتائج: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للرفع على Hugging Face")
        print("\n📋 الخطوات التالية:")
        print("1. تأكد من وجود جميع الملفات المطلوبة")
        print("2. ارفع المشروع على Hugging Face Spaces")
        print("3. اختر SDK: Gradio")
        print("4. أضف مفاتيح API في إعدادات Space")
        print("5. شغل التطبيق واستمتع!")
    else:
        print("⚠️ بعض الاختبارات فشلت - راجع الأخطاء أعلاه")
    
    return passed == total

if __name__ == "__main__":
    main()
