# 🚀 دليل نشر وكيل أخبار الألعاب

## 📋 نظرة عامة

هذا الدليل يوضح كيفية نشر وكيل أخبار الألعاب على منصات الاستضافة المختلفة. الوكيل يعمل بـ `main.py` كملف رئيسي مع واجهة ويب تفاعلية.

## ✅ متطلبات النشر

### الملفات الأساسية
- `main.py` - الملف الرئيسي للوكيل
- `web_api.py` - خادم الواجهة الويب
- `deployment_config.py` - إعدادات النشر
- `requirements.txt` - متطلبات Python
- `Procfile` - ملف تشغيل Heroku
- `render.yaml` - ملف تكوين Render

### المجلدات المطلوبة
- `modules/` - وحدات الوكيل
- `config/` - مل<PERSON><PERSON>ت التكوين
- `web_interface/` - ملفات الواجهة الويب
- `data/` - قاعدة البيانات (ستُنشأ تلقائياً)
- `logs/` - ملفات السجلات (ستُنشأ تلقائياً)

## 🎯 النشر على Render (الأفضل)

### لماذا Render؟
- ✅ التشغيل المستمر 24/7
- ✅ 750 ساعة مجانية شهرياً
- ✅ دعم Python والمكتبات
- ✅ واجهة ويب سهلة
- ✅ HTTPS تلقائي

### خطوات النشر

#### 1. إعداد المستودع
```bash
# إنشاء مستودع Git جديد
git init
git add .
git commit -m "Initial commit"

# رفع إلى GitHub
git remote add origin https://github.com/username/gaming-news-agent.git
git push -u origin main
```

#### 2. إنشاء خدمة على Render
1. اذهب إلى [render.com](https://render.com)
2. سجل دخول أو أنشئ حساب جديد
3. اضغط "New" ثم "Web Service"
4. اربط مستودع GitHub الخاص بك
5. اختر المستودع الذي يحتوي على الوكيل

#### 3. إعدادات الخدمة
- **Name**: `gaming-news-agent`
- **Environment**: `Python 3`
- **Build Command**: `pip install -r requirements.txt`
- **Start Command**: `python main.py`
- **Plan**: `Free`

#### 4. متغيرات البيئة
أضف المتغيرات التالية في قسم Environment Variables:

```
RENDER=true
PYTHONUNBUFFERED=1
PORT=10000

# مفاتيح API (أضف قيمك الحقيقية)
GEMINI_API_KEY=your_gemini_key_here
TELEGRAM_BOT_TOKEN=your_telegram_token_here
BLOGGER_CLIENT_ID=your_blogger_client_id
BLOGGER_CLIENT_SECRET=your_blogger_client_secret
OPENART_API_KEY=your_openart_key_here
LEAP_AI_API_KEY=your_leap_ai_key_here
DEEPAI_API_KEY=your_deepai_key_here
REPLICATE_API_TOKEN=your_replicate_token_here
NEWSDATA_API_KEY=your_newsdata_key_here
SERPAPI_KEY=your_serpapi_key_here
TAVILY_API_KEY=your_tavily_key_here
```

#### 5. النشر
1. اضغط "Create Web Service"
2. انتظر حتى يكتمل البناء والنشر
3. ستحصل على رابط مثل: `https://gaming-news-agent.onrender.com`

## 🌐 النشر على Heroku

### خطوات النشر
```bash
# تثبيت Heroku CLI
# ثم تسجيل الدخول
heroku login

# إنشاء تطبيق جديد
heroku create gaming-news-agent

# إضافة متغيرات البيئة
heroku config:set HEROKU=true
heroku config:set PYTHONUNBUFFERED=1
heroku config:set GEMINI_API_KEY=your_key_here
# أضف باقي المفاتيح...

# نشر التطبيق
git push heroku main
```

## 🚂 النشر على Railway

### خطوات النشر
1. اذهب إلى [railway.app](https://railway.app)
2. اربط مستودع GitHub
3. اختر المستودع
4. أضف متغيرات البيئة
5. انشر التطبيق

## 🔧 إعدادات ما بعد النشر

### 1. فحص الصحة
تأكد من أن الرابط `/health` يعمل:
```
https://your-app-url.com/health
```

### 2. الواجهة الويب
الواجهة متاحة على:
```
https://your-app-url.com/
```

### 3. مراقبة السجلات
- **Render**: Dashboard > Service > Logs
- **Heroku**: `heroku logs --tail`
- **Railway**: Dashboard > Deployments > Logs

### 4. إعداد نظام منع النوم (لـ Render)
الوكيل يتضمن نظام ping تلقائي لمنع النوم، ولكن يمكنك أيضاً:

#### استخدام UptimeRobot (مجاني)
1. اذهب إلى [uptimerobot.com](https://uptimerobot.com)
2. أنشئ حساب مجاني
3. أضف مراقب جديد:
   - **Type**: HTTP(s)
   - **URL**: `https://your-app-url.com/health`
   - **Monitoring Interval**: 5 دقائق
4. احفظ الإعدادات

#### استخدام Cron-job.org (مجاني)
1. اذهب إلى [cron-job.org](https://cron-job.org)
2. أنشئ حساب مجاني
3. أضف cronjob جديد:
   - **URL**: `https://your-app-url.com/health`
   - **Schedule**: كل 10 دقائق
4. فعّل الـ cronjob

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في بناء التطبيق
```bash
# تأكد من صحة requirements.txt
pip install -r requirements.txt

# اختبر محلياً
python test_deployment.py
```

#### 2. خطأ في بدء التشغيل
- تأكد من أن `main.py` يعمل محلياً
- فحص متغيرات البيئة
- مراجعة السجلات

#### 3. مشاكل قاعدة البيانات
- تأكد من وجود مجلد `data/`
- فحص أذونات الكتابة
- مراجعة إعدادات قاعدة البيانات

#### 4. مشاكل الواجهة الويب
- تأكد من وجود ملفات `web_interface/`
- فحص إعدادات Flask
- مراجعة إعدادات CORS

## 📊 مراقبة الأداء

### مؤشرات مهمة
- **وقت التشغيل**: يجب أن يكون 99%+
- **استخدام الذاكرة**: أقل من 512MB
- **زمن الاستجابة**: أقل من 2 ثانية
- **معدل الأخطاء**: أقل من 1%

### أدوات المراقبة
- **Render**: مراقبة مدمجة
- **Heroku**: Heroku Metrics
- **Railway**: مراقبة مدمجة

## 🔐 الأمان

### أفضل الممارسات
1. **لا تضع مفاتيح API في الكود**
2. **استخدم متغيرات البيئة دائماً**
3. **فعّل HTTPS**
4. **راقب السجلات بانتظام**
5. **حدّث المكتبات دورياً**

## 📞 الدعم

### في حالة المشاكل
1. راجع السجلات أولاً
2. تأكد من إعدادات متغيرات البيئة
3. اختبر محلياً باستخدام `test_deployment.py`
4. راجع وثائق المنصة المستخدمة

## 🎉 تهانينا!

إذا وصلت إلى هنا، فقد نجحت في نشر وكيل أخبار الألعاب! 

الوكيل الآن يعمل 24/7 وينشر أخبار الألعاب تلقائياً مع واجهة ويب للمراقبة والتحكم.

---

**ملاحظة**: تأكد من مراجعة وتحديث مفاتيح API بانتظام للحفاظ على أمان النظام.
