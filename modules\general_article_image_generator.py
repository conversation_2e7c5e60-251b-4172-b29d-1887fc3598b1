"""
نظام إنشاء صور للمقالات العامة
يتعامل مع المقالات التي لا تتعلق بلعبة واحدة محددة مثل "أفضل 5 ألعاب"
"""

import os
import re
import asyncio
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
import aiohttp
import json

from modules.logger import logger
from modules.licensed_image_manager import licensed_image_manager
from modules.manual_image_generator import manual_image_generator
from config.settings import BotConfig


class GeneralArticleImageGenerator:
    """مولد الصور للمقالات العامة"""
    
    def __init__(self):
        self.output_dir = "images/general_articles"
        self.backgrounds_dir = "assets/backgrounds/general"
        self.fonts_dir = "font"
        self.image_size = (1200, 630)  # مقاس مناسب لوسائل التواصل
        
        # إنشاء المجلدات المطلوبة
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.backgrounds_dir, exist_ok=True)
        
        # إعدادات الألوان
        self.color_schemes = {
            'gaming': {
                'primary': '#FF6B35',
                'secondary': '#004E89',
                'accent': '#FFD23F',
                'text': '#FFFFFF',
                'overlay': (0, 0, 0, 120)
            },
            'tech': {
                'primary': '#00D4FF',
                'secondary': '#1A1A2E',
                'accent': '#16213E',
                'text': '#FFFFFF',
                'overlay': (26, 26, 46, 140)
            },
            'news': {
                'primary': '#E94560',
                'secondary': '#0F3460',
                'accent': '#16213E',
                'text': '#FFFFFF',
                'overlay': (15, 52, 96, 130)
            }
        }
        
        logger.info("🎨 تم تهيئة مولد صور المقالات العامة")

    async def generate_image_for_general_article(self, article: Dict) -> Optional[Dict]:
        """إنشاء صورة للمقال العام"""
        try:
            title = article.get('title', '')
            logger.info(f"🎨 بدء إنشاء صورة للمقال العام: {title[:50]}...")
            
            # تحديد نوع المقال
            article_type = self._detect_article_type(article)
            
            # استخراج الألعاب المذكورة في المقال
            mentioned_games = self._extract_mentioned_games(article)
            
            if mentioned_games:
                # إنشاء صورة مركبة مع صور الألعاب
                return await self._create_composite_image_with_games(article, mentioned_games, article_type)
            else:
                # إنشاء صورة عامة بالذكاء الاصطناعي + تصميم يدوي
                return await self._create_general_ai_image(article, article_type)
                
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء صورة المقال العام: {e}")
            return None

    def _detect_article_type(self, article: Dict) -> str:
        """تحديد نوع المقال"""
        try:
            title = article.get('title', '').lower()
            content = article.get('content', '').lower()
            text = f"{title} {content}"
            
            # أنماط مختلفة من المقالات
            if any(keyword in text for keyword in ['أفضل', 'best', 'top', 'قائمة', 'list']):
                return 'list'
            elif any(keyword in text for keyword in ['مراجعة', 'review', 'تقييم', 'rating']):
                return 'review'
            elif any(keyword in text for keyword in ['أخبار', 'news', 'إعلان', 'announcement']):
                return 'news'
            elif any(keyword in text for keyword in ['دليل', 'guide', 'شرح', 'tutorial']):
                return 'guide'
            elif any(keyword in text for keyword in ['مقارنة', 'comparison', 'vs', 'ضد']):
                return 'comparison'
            else:
                return 'general'
                
        except Exception as e:
            logger.warning(f"خطأ في تحديد نوع المقال: {e}")
            return 'general'

    def _extract_mentioned_games(self, article: Dict) -> List[str]:
        """استخراج أسماء الألعاب المذكورة في المقال"""
        try:
            title = article.get('title', '')
            content = article.get('content', '')
            text = f"{title} {content}"
            
            # قائمة الألعاب الشهيرة للبحث عنها
            popular_games = [
                'Fortnite', 'Minecraft', 'Call of Duty', 'FIFA', 'GTA', 'Grand Theft Auto',
                'Cyberpunk', 'The Witcher', 'Overwatch', 'League of Legends', 'Valorant',
                'Apex Legends', 'Destiny', 'Halo', 'God of War', 'Spider-Man', 'Horizon',
                'The Last of Us', 'Uncharted', 'Zelda', 'Mario', 'Pokemon', 'Genshin Impact',
                'Elden Ring', 'Dark Souls', 'Sekiro', 'Bloodborne', 'Red Dead Redemption',
                'Assassin\'s Creed', 'Far Cry', 'Watch Dogs', 'Rainbow Six', 'Battlefield',
                'Counter-Strike', 'Dota', 'World of Warcraft', 'Diablo', 'StarCraft'
            ]
            
            mentioned = []
            for game in popular_games:
                if game.lower() in text.lower():
                    mentioned.append(game)
            
            # إزالة المكررات والحفاظ على الترتيب
            unique_mentioned = []
            for game in mentioned:
                if game not in unique_mentioned:
                    unique_mentioned.append(game)
            
            logger.info(f"🎮 الألعاب المذكورة: {unique_mentioned}")
            return unique_mentioned[:5]  # أقصى 5 ألعاب
            
        except Exception as e:
            logger.warning(f"خطأ في استخراج أسماء الألعاب: {e}")
            return []

    async def _create_composite_image_with_games(self, article: Dict, games: List[str], article_type: str) -> Optional[Dict]:
        """إنشاء صورة مركبة تتضمن صور الألعاب المذكورة"""
        try:
            logger.info(f"🎨 إنشاء صورة مركبة مع {len(games)} لعبة")
            
            # الحصول على صور الألعاب المرخصة
            game_images = []
            for game in games:
                try:
                    images = await licensed_image_manager.get_licensed_images_for_game(game, 1)
                    if images:
                        game_images.append({
                            'game': game,
                            'image': images[0]
                        })
                except Exception as e:
                    logger.warning(f"فشل في الحصول على صورة {game}: {e}")
            
            if not game_images:
                logger.warning("لم يتم العثور على صور للألعاب، التحول للطريقة العامة")
                return await self._create_general_ai_image(article, article_type)
            
            # إنشاء خلفية بالذكاء الاصطناعي
            ai_background = await self._generate_ai_background(article, article_type)
            
            if ai_background:
                # دمج صور الألعاب مع الخلفية
                composite_image = await self._compose_image_with_games(
                    ai_background, game_images, article.get('title', ''), article_type
                )
                
                if composite_image:
                    return composite_image
            
            # إذا فشل الذكاء الاصطناعي، استخدم التصميم اليدوي
            return await self._create_manual_composite_image(article, game_images, article_type)
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء الصورة المركبة: {e}")
            return None

    async def _generate_ai_background(self, article: Dict, article_type: str) -> Optional[str]:
        """إنشاء خلفية بالذكاء الاصطناعي"""
        try:
            # إنشاء prompt مناسب للخلفية
            title = article.get('title', '')
            
            prompts_by_type = {
                'list': f"Professional gaming background for article about {title}, modern gaming setup, multiple monitors, RGB lighting, dark theme, high quality, 4K",
                'review': f"Gaming review background, professional gaming environment, clean modern design, subtle gaming elements",
                'news': f"Gaming news background, futuristic gaming theme, technology elements, professional news style",
                'guide': f"Gaming tutorial background, educational gaming setup, clean interface design, helpful atmosphere",
                'comparison': f"Gaming comparison background, side-by-side gaming setup, balanced composition, professional",
                'general': f"General gaming background, modern gaming theme, professional esports environment, clean design"
            }
            
            prompt = prompts_by_type.get(article_type, prompts_by_type['general'])
            
            # استخدام Pollinations.AI لإنشاء الخلفية
            background_url = await self._generate_with_pollinations(prompt)
            
            if background_url:
                logger.info("✅ تم إنشاء خلفية بالذكاء الاصطناعي")
                return background_url
            
            return None
            
        except Exception as e:
            logger.warning(f"فشل في إنشاء خلفية AI: {e}")
            return None

    async def _generate_with_pollinations(self, prompt: str) -> Optional[str]:
        """إنشاء صورة باستخدام Pollinations.AI"""
        try:
            import urllib.parse
            
            # تحسين الـ prompt
            optimized_prompt = f"{prompt}, professional, high quality, 4K, detailed, gaming theme"
            encoded_prompt = urllib.parse.quote(optimized_prompt)
            
            # إنشاء URL للصورة
            image_url = f"https://image.pollinations.ai/prompt/{encoded_prompt}"
            
            # إضافة معاملات الجودة
            params = "?width=1200&height=630&model=flux&enhance=true"
            full_url = image_url + params
            
            # التحقق من أن الصورة تم إنشاؤها بنجاح
            async with aiohttp.ClientSession() as session:
                async with session.get(full_url) as response:
                    if response.status == 200:
                        return full_url
            
            return None
            
        except Exception as e:
            logger.warning(f"فشل في Pollinations.AI: {e}")
            return None

    async def _create_general_ai_image(self, article: Dict, article_type: str) -> Optional[Dict]:
        """إنشاء صورة عامة بالذكاء الاصطناعي + تصميم يدوي"""
        try:
            logger.info("🎨 إنشاء صورة عامة بالذكاء الاصطناعي")
            
            # إنشاء خلفية بالذكاء الاصطناعي
            ai_background = await self._generate_ai_background(article, article_type)
            
            if ai_background:
                # إضافة النص والتحسينات اليدوية
                final_image = await self._enhance_ai_background_with_text(
                    ai_background, article.get('title', ''), article_type
                )
                
                if final_image:
                    return final_image
            
            # إذا فشل الذكاء الاصطناعي، استخدم التصميم اليدوي بالكامل
            logger.info("🔄 التحول للتصميم اليدوي")
            return await manual_image_generator.generate_manual_image(article)
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء الصورة العامة: {e}")
            return None


    async def _compose_image_with_games(self, background_url: str, game_images: List[Dict], title: str, article_type: str) -> Optional[Dict]:
        """دمج صور الألعاب مع الخلفية"""
        try:
            # تحميل الخلفية
            async with aiohttp.ClientSession() as session:
                async with session.get(background_url) as response:
                    if response.status == 200:
                        background_data = await response.read()

                        # حفظ الخلفية مؤقتاً
                        temp_bg_path = os.path.join(self.output_dir, "temp_bg.jpg")
                        with open(temp_bg_path, 'wb') as f:
                            f.write(background_data)

                        # فتح الخلفية
                        with Image.open(temp_bg_path) as bg_img:
                            bg_img = bg_img.convert('RGB')
                            bg_img = bg_img.resize(self.image_size, Image.Resampling.LANCZOS)

                            # إضافة طبقة شفافة
                            overlay = Image.new('RGBA', self.image_size, self.color_schemes['gaming']['overlay'])
                            bg_rgba = bg_img.convert('RGBA')
                            bg_with_overlay = Image.alpha_composite(bg_rgba, overlay)

                            # إضافة صور الألعاب
                            final_img = await self._add_game_images_to_background(bg_with_overlay, game_images)

                            # إضافة النص
                            final_img = self._add_text_to_image(final_img, title, article_type)

                            # حفظ الصورة النهائية
                            filename = f"general_{hashlib.md5(title.encode()).hexdigest()[:8]}.png"
                            filepath = os.path.join(self.output_dir, filename)
                            final_img.save(filepath, 'PNG', quality=95)

                            # تنظيف الملف المؤقت
                            os.remove(temp_bg_path)

                            return {
                                'url': f"file://{os.path.abspath(filepath)}",
                                'local_path': filepath,
                                'filename': filename,
                                'description': f'Composite image for: {title[:50]}...',
                                'source': 'AI + Manual Composition',
                                'license': 'Generated Content',
                                'attribution': f'Created by {getattr(BotConfig, "WEBSITE_NAME", "Gaming News")}',
                                'width': self.image_size[0],
                                'height': self.image_size[1],
                                'format': 'PNG',
                                'generation_method': 'ai_manual_composite',
                                'games_included': [img['game'] for img in game_images],
                                'creation_date': datetime.now().isoformat()
                            }

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في دمج الصور: {e}")
            return None

    async def _add_game_images_to_background(self, background: Image.Image, game_images: List[Dict]) -> Image.Image:
        """إضافة صور الألعاب للخلفية"""
        try:
            # حساب مواضع الصور
            num_games = len(game_images)
            if num_games == 0:
                return background

            # مقاسات الصور الصغيرة
            small_size = (150, 150)

            # حساب المواضع
            positions = self._calculate_game_image_positions(num_games, small_size)

            for i, game_data in enumerate(game_images[:len(positions)]):
                try:
                    # تحميل صورة اللعبة
                    game_image_url = game_data['image'].url

                    async with aiohttp.ClientSession() as session:
                        async with session.get(game_image_url) as response:
                            if response.status == 200:
                                game_img_data = await response.read()

                                # حفظ مؤقت
                                temp_game_path = os.path.join(self.output_dir, f"temp_game_{i}.jpg")
                                with open(temp_game_path, 'wb') as f:
                                    f.write(game_img_data)

                                # فتح وتحسين صورة اللعبة
                                with Image.open(temp_game_path) as game_img:
                                    game_img = game_img.convert('RGB')
                                    game_img = game_img.resize(small_size, Image.Resampling.LANCZOS)

                                    # إضافة حدود
                                    bordered_img = self._add_border_to_game_image(game_img)

                                    # لصق الصورة في الموضع المحدد
                                    background.paste(bordered_img, positions[i])

                                # تنظيف
                                os.remove(temp_game_path)

                except Exception as e:
                    logger.warning(f"فشل في إضافة صورة اللعبة {game_data['game']}: {e}")
                    continue

            return background

        except Exception as e:
            logger.error(f"❌ خطأ في إضافة صور الألعاب: {e}")
            return background

    def _calculate_game_image_positions(self, num_games: int, image_size: Tuple[int, int]) -> List[Tuple[int, int]]:
        """حساب مواضع صور الألعاب"""
        positions = []
        img_width, img_height = image_size
        canvas_width, canvas_height = self.image_size

        # هامش من الحواف
        margin = 20

        if num_games == 1:
            # وسط يمين
            x = canvas_width - img_width - margin
            y = (canvas_height - img_height) // 2
            positions.append((x, y))

        elif num_games == 2:
            # يمين أعلى ويمين أسفل
            x = canvas_width - img_width - margin
            y1 = margin + 50
            y2 = canvas_height - img_height - margin - 50
            positions.extend([(x, y1), (x, y2)])

        elif num_games <= 5:
            # توزيع في الجانب الأيمن
            spacing = (canvas_height - 2 * margin - num_games * img_height) // (num_games - 1) if num_games > 1 else 0
            x = canvas_width - img_width - margin

            for i in range(num_games):
                y = margin + i * (img_height + spacing)
                positions.append((x, y))

        return positions

    def _add_border_to_game_image(self, image: Image.Image) -> Image.Image:
        """إضافة حدود لصورة اللعبة"""
        try:
            # إنشاء صورة جديدة بحدود
            border_width = 3
            new_size = (image.width + 2 * border_width, image.height + 2 * border_width)
            bordered = Image.new('RGB', new_size, '#FFD23F')  # لون الحدود

            # لصق الصورة الأصلية في الوسط
            bordered.paste(image, (border_width, border_width))

            return bordered

        except Exception as e:
            logger.warning(f"فشل في إضافة حدود: {e}")
            return image

    def _add_text_to_image(self, image: Image.Image, title: str, article_type: str) -> Image.Image:
        """إضافة النص للصورة"""
        try:
            draw = ImageDraw.Draw(image)

            # تحديد منطقة النص (الجانب الأيسر)
            text_area_width = self.image_size[0] - 200  # ترك مساحة للصور

            # تحسين النص للعرض
            display_title = self._prepare_title_for_display(title)

            # رسم النص
            self._draw_title_text(draw, display_title, text_area_width, article_type)

            return image

        except Exception as e:
            logger.error(f"❌ خطأ في إضافة النص: {e}")
            return image

    def _prepare_title_for_display(self, title: str) -> str:
        """تحضير العنوان للعرض"""
        # تقصير العنوان إذا كان طويلاً
        if len(title) > 60:
            title = title[:57] + "..."

        return title

    def _draw_title_text(self, draw: ImageDraw.Draw, title: str, max_width: int, article_type: str):
        """رسم نص العنوان"""
        try:
            # اختيار نظام الألوان
            colors = self.color_schemes.get(article_type, self.color_schemes['gaming'])

            # محاولة تحميل خط مناسب
            try:
                font_size = 48
                font = ImageFont.truetype(os.path.join(self.fonts_dir, "arabic/NotoSansArabic-Bold.ttf"), font_size)
            except:
                try:
                    font = ImageFont.truetype("arial.ttf", 48)
                except:
                    font = ImageFont.load_default()

            # رسم النص مع ظل
            text_x = 30
            text_y = self.image_size[1] // 2 - 30

            # رسم الظل
            draw.text((text_x + 2, text_y + 2), title, font=font, fill='#000000')
            # رسم النص الأساسي
            draw.text((text_x, text_y), title, font=font, fill=colors['text'])

        except Exception as e:
            logger.warning(f"فشل في رسم النص: {e}")

    async def _create_manual_composite_image(self, article: Dict, game_images: List[Dict], article_type: str) -> Optional[Dict]:
        """إنشاء صورة مركبة يدوياً بالكامل"""
        try:
            logger.info("🎨 إنشاء صورة مركبة يدوياً")

            # استخدام مولد الصور اليدوي مع تحسينات
            manual_result = await manual_image_generator.generate_manual_image(article)

            if manual_result:
                # تحسين الصورة اليدوية بإضافة صور الألعاب
                enhanced_result = await self._enhance_manual_image_with_games(manual_result, game_images)
                return enhanced_result or manual_result

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء الصورة اليدوية المركبة: {e}")
            return None

    async def _enhance_manual_image_with_games(self, manual_result: Dict, game_images: List[Dict]) -> Optional[Dict]:
        """تحسين الصورة اليدوية بإضافة صور الألعاب"""
        try:
            if not game_images or not manual_result.get('local_path'):
                return None

            # فتح الصورة اليدوية
            with Image.open(manual_result['local_path']) as manual_img:
                manual_img = manual_img.convert('RGBA')

                # إضافة صور الألعاب
                enhanced_img = await self._add_game_images_to_background(manual_img, game_images)

                # حفظ الصورة المحسنة
                filename = f"enhanced_{manual_result['filename']}"
                filepath = os.path.join(self.output_dir, filename)
                enhanced_img.save(filepath, 'PNG', quality=95)

                # تحديث النتيجة
                enhanced_result = manual_result.copy()
                enhanced_result.update({
                    'url': f"file://{os.path.abspath(filepath)}",
                    'local_path': filepath,
                    'filename': filename,
                    'generation_method': 'manual_enhanced_with_games',
                    'games_included': [img['game'] for img in game_images]
                })

                return enhanced_result

        except Exception as e:
            logger.warning(f"فشل في تحسين الصورة اليدوية: {e}")
            return None

    async def _enhance_ai_background_with_text(self, background_url: str, title: str, article_type: str) -> Optional[Dict]:
        """تحسين خلفية الذكاء الاصطناعي بإضافة النص"""
        try:
            # تحميل الخلفية
            async with aiohttp.ClientSession() as session:
                async with session.get(background_url) as response:
                    if response.status == 200:
                        background_data = await response.read()

                        # حفظ مؤقت
                        temp_path = os.path.join(self.output_dir, "temp_ai_bg.jpg")
                        with open(temp_path, 'wb') as f:
                            f.write(background_data)

                        # فتح وتحسين
                        with Image.open(temp_path) as bg_img:
                            bg_img = bg_img.convert('RGB')
                            bg_img = bg_img.resize(self.image_size, Image.Resampling.LANCZOS)

                            # إضافة طبقة شفافة للنص
                            overlay = Image.new('RGBA', self.image_size, self.color_schemes['gaming']['overlay'])
                            bg_rgba = bg_img.convert('RGBA')
                            bg_with_overlay = Image.alpha_composite(bg_rgba, overlay)

                            # إضافة النص
                            final_img = self._add_text_to_image(bg_with_overlay, title, article_type)

                            # حفظ النتيجة
                            filename = f"ai_enhanced_{hashlib.md5(title.encode()).hexdigest()[:8]}.png"
                            filepath = os.path.join(self.output_dir, filename)
                            final_img.save(filepath, 'PNG', quality=95)

                            # تنظيف
                            os.remove(temp_path)

                            return {
                                'url': f"file://{os.path.abspath(filepath)}",
                                'local_path': filepath,
                                'filename': filename,
                                'description': f'AI enhanced image for: {title[:50]}...',
                                'source': 'AI + Manual Enhancement',
                                'license': 'Generated Content',
                                'attribution': f'Created by {getattr(BotConfig, "WEBSITE_NAME", "Gaming News")}',
                                'width': self.image_size[0],
                                'height': self.image_size[1],
                                'format': 'PNG',
                                'generation_method': 'ai_enhanced_with_text',
                                'creation_date': datetime.now().isoformat()
                            }

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في تحسين خلفية AI: {e}")
            return None


# إنشاء كائن مولد صور المقالات العامة
general_article_image_generator = GeneralArticleImageGenerator()
