#!/usr/bin/env python3
# اختبار تحسينات المحتوى الجديدة

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.internal_links_manager import internal_links_manager
from modules.keywords_enhancer import keywords_enhancer
from modules.natural_writing_enhancer import natural_writing_enhancer
from config.content_enhancement_config import get_enhancement_config

def test_internal_links():
    """اختبار نظام الروابط الداخلية"""
    print("🔗 اختبار نظام الروابط الداخلية...")
    
    sample_content = """
    أعلنت شركة Sony عن إصدار جديد للعبة God of War على منصة PlayStation 5.
    كما أكدت Microsoft أن لعبة Halo ستكون متاحة على Xbox Series X قريباً.
    وفي الوقت نفسه، تعمل Nintendo على تطوير ألعاب جديدة لجهاز Nintendo Switch.
    """
    
    enhanced_content, links_data = internal_links_manager.add_internal_links_to_content(sample_content)
    
    print(f"✅ تم إضافة {links_data['total_links']} رابط داخلي")
    print(f"📊 الشركات: {len(links_data['companies'])}")
    print(f"🎮 الألعاب: {len(links_data['games'])}")
    print(f"🖥️ المنصات: {len(links_data['platforms'])}")
    print(f"👤 الأشخاص: {len(links_data['persons'])}")
    
    print("\n📝 المحتوى المحسن:")
    print(enhanced_content)
    print("\n" + "="*50)

def test_keywords_enhancement():
    """اختبار تحسين الكلمات المفتاحية"""
    print("🔍 اختبار تحسين الكلمات المفتاحية...")
    
    original_keywords = ["ألعاب", "PlayStation", "أخبار"]
    sample_content = """
    أعلنت شركة Sony عن إصدار جديد للعبة God of War على منصة PlayStation 5.
    هذا الإصدار يتضمن تحسينات في الجرافيك والأداء، ويقدم تجربة لعب محسنة للاعبين.
    """
    
    enhanced_keywords = keywords_enhancer.enhance_keywords(
        original_keywords, sample_content, 'news', 15
    )
    
    print(f"✅ تم تحسين الكلمات المفتاحية من {len(original_keywords)} إلى {len(enhanced_keywords)}")
    print("📋 الكلمات المفتاحية الأصلية:", original_keywords)
    print("🔧 الكلمات المفتاحية المحسنة:", enhanced_keywords)
    
    # اختبار تنسيق قسم الكلمات المفتاحية
    keywords_section = keywords_enhancer.format_keywords_section(enhanced_keywords)
    print("\n🎨 قسم الكلمات المفتاحية المنسق:")
    print(keywords_section)
    print("\n" + "="*50)

def test_natural_writing():
    """اختبار تحسين الكتابة الطبيعية"""
    print("✍️ اختبار تحسين الكتابة الطبيعية...")
    
    sample_content = """
    هذا المقال يتحدث عن أحدث الألعاب التي تم إصدارها مؤخراً.
    يمكن للاعبين أن يجدوا في هذه الألعاب تجربة ممتعة جداً.
    لأن هذه الألعاب تحتوي على قصص رائعة وجرافيك عالي الجودة.
    """
    
    # اختبار اللهجة المصرية
    print("🇪🇬 اختبار اللهجة المصرية:")
    egyptian_content = natural_writing_enhancer.enhance_natural_writing(
        sample_content, 'egyptian', 0.4
    )
    print(egyptian_content)
    
    print("\n🇸🇦 اختبار اللهجة السعودية:")
    saudi_content = natural_writing_enhancer.enhance_natural_writing(
        sample_content, 'saudi', 0.4
    )
    print(saudi_content)
    
    print("\n📚 اختبار العربية المبسطة:")
    standard_content = natural_writing_enhancer.enhance_natural_writing(
        sample_content, 'standard', 0.3
    )
    print(standard_content)
    print("\n" + "="*50)

def test_complete_enhancement():
    """اختبار التحسين الشامل"""
    print("🚀 اختبار التحسين الشامل...")
    
    sample_article = {
        'title': 'أحدث أخبار ألعاب PlayStation 5',
        'content': """
        أعلنت شركة Sony عن مجموعة جديدة من الألعاب الحصرية لجهاز PlayStation 5.
        تتضمن هذه الألعاب God of War Ragnarök و Spider-Man 2 و Horizon Forbidden West.
        
        هذه الألعاب تستفيد من قوة معالج PlayStation 5 لتقديم تجربة لعب استثنائية.
        يمكن للاعبين أن يتوقعوا جرافيك عالي الدقة وأوقات تحميل سريعة جداً.
        
        كما أكدت Sony أن هذه الألعاب ستكون متاحة حصرياً على PlayStation 5 في البداية.
        لكن بعض الألعاب قد تصل إلى PC في وقت لاحق.
        """,
        'keywords': ['PlayStation 5', 'Sony', 'ألعاب حصرية']
    }
    
    # تطبيق جميع التحسينات
    print("🔗 إضافة الروابط الداخلية...")
    enhanced_content, links_data = internal_links_manager.add_internal_links_to_content(
        sample_article['content']
    )
    
    print("🔍 تحسين الكلمات المفتاحية...")
    enhanced_keywords = keywords_enhancer.enhance_keywords(
        sample_article['keywords'], enhanced_content, 'news', 12
    )
    
    print("🎨 إضافة قسم الكلمات المفتاحية...")
    keywords_section = keywords_enhancer.format_keywords_section(enhanced_keywords)
    enhanced_content += keywords_section
    
    print("✍️ تحسين الكتابة الطبيعية...")
    final_content = natural_writing_enhancer.enhance_natural_writing(
        enhanced_content, 'egyptian', 0.25
    )
    
    print("\n📊 نتائج التحسين:")
    print(f"✅ الروابط الداخلية: {links_data['total_links']}")
    print(f"✅ الكلمات المفتاحية: {len(enhanced_keywords)}")
    print(f"✅ طول المحتوى: {len(final_content)} حرف")
    
    print("\n📝 المحتوى النهائي المحسن:")
    print("="*60)
    print(final_content)
    print("="*60)

def test_config_system():
    """اختبار نظام الإعدادات"""
    print("⚙️ اختبار نظام الإعدادات...")
    
    # اختبار إعدادات مختلفة
    news_config = get_enhancement_config('gaming_news', 'egyptian')
    review_config = get_enhancement_config('game_reviews', 'saudi')
    guide_config = get_enhancement_config('gaming_guides', 'standard')
    
    print("✅ تم تحميل إعدادات الأخبار")
    print("✅ تم تحميل إعدادات المراجعات")
    print("✅ تم تحميل إعدادات الأدلة")
    
    print(f"📊 عدد الكلمات المفتاحية القصوى: {news_config['keywords']['max_keywords']}")
    print(f"🔗 عدد الروابط الداخلية القصوى: {news_config['internal_links']['max_links_per_article']}")
    print(f"✍️ معدل الأخطاء الطبيعية: {news_config['natural_writing']['error_rate']}")
    print("\n" + "="*50)

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار تحسينات المحتوى الجديدة")
    print("="*60)
    
    try:
        test_internal_links()
        test_keywords_enhancement()
        test_natural_writing()
        test_config_system()
        test_complete_enhancement()
        
        print("\n🎉 تم اجتياز جميع الاختبارات بنجاح!")
        print("✅ النظام جاهز للاستخدام")
        
    except Exception as e:
        print(f"\n❌ فشل في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
