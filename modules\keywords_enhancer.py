# نظام تحسين الكلمات المفتاحية
import re
import random
from typing import List, Dict, Set
from .logger import logger

class KeywordsEnhancer:
    """محسن الكلمات المفتاحية للمقالات"""
    
    def __init__(self):
        # الكلمات المفتاحية الأساسية للألعاب
        self.base_gaming_keywords = [
            "ألعاب الفيديو", "أنواع الألعاب", "ألعاب", "PC Gaming", 
            "إصدارات جديدة", "دليل الألعاب", "ألعاب الفيديو للمبتدئين",
            "console games", "free games", "gaming news", "تحديثات الألعاب",
            "offline games", "تحديث جديد", "mobile games", "best games",
            "gaming tips", "game reviews", "ألعاب مجانية", "ألعاب أونلاين",
            "gaming hardware", "gaming setup", "esports", "الرياضات الإلكترونية",
            "streaming games", "game development", "indie games", "AAA games"
        ]
        
        # كلمات مفتاحية حسب نوع المحتوى
        self.content_type_keywords = {
            'news': [
                "أخبار الألعاب", "gaming news", "game announcements", 
                "إعلانات الألعاب", "أحدث الأخبار", "breaking news",
                "game industry news", "تطورات الألعاب"
            ],
            'review': [
                "مراجعة لعبة", "game review", "تقييم الألعاب", 
                "نقد الألعاب", "game rating", "gameplay review",
                "graphics review", "story review"
            ],
            'guide': [
                "دليل الألعاب", "game guide", "gaming tutorial", 
                "نصائح الألعاب", "gaming tips", "walkthrough",
                "strategy guide", "beginner guide"
            ],
            'update': [
                "تحديث اللعبة", "game update", "patch notes", 
                "تحديثات جديدة", "game patches", "version update",
                "content update", "bug fixes"
            ]
        }
        
        # كلمات مفتاحية حسب المنصة
        self.platform_keywords = {
            'playstation': [
                "PlayStation", "PS5", "PS4", "Sony", "PlayStation games",
                "PlayStation exclusives", "PlayStation Store"
            ],
            'xbox': [
                "Xbox", "Xbox Series X", "Xbox Series S", "Microsoft",
                "Xbox Game Pass", "Xbox exclusives", "Xbox Store"
            ],
            'nintendo': [
                "Nintendo", "Nintendo Switch", "Nintendo games",
                "Nintendo exclusives", "Nintendo eShop"
            ],
            'pc': [
                "PC gaming", "Steam", "Epic Games", "PC games",
                "computer games", "Windows gaming", "gaming PC"
            ],
            'mobile': [
                "mobile games", "ألعاب الجوال", "Android games",
                "iOS games", "smartphone games", "tablet games"
            ]
        }
        
        # كلمات مفتاحية حسب النوع
        self.genre_keywords = {
            'action': [
                "action games", "ألعاب الأكشن", "fighting games",
                "shooter games", "adventure games"
            ],
            'rpg': [
                "RPG games", "role playing games", "ألعاب تقمص الأدوار",
                "fantasy games", "character development"
            ],
            'strategy': [
                "strategy games", "ألعاب الاستراتيجية", "tactical games",
                "real-time strategy", "turn-based strategy"
            ],
            'sports': [
                "sports games", "ألعاب رياضية", "football games",
                "basketball games", "racing games"
            ],
            'simulation': [
                "simulation games", "ألعاب المحاكاة", "life simulation",
                "city building", "farming games"
            ]
        }
    
    def enhance_keywords(self, original_keywords: List[str], content: str, 
                        content_type: str = 'news', max_keywords: int = 15) -> List[str]:
        """تحسين قائمة الكلمات المفتاحية"""
        try:
            enhanced_keywords = set()
            
            # إضافة الكلمات المفتاحية الأصلية
            for keyword in original_keywords:
                if keyword and len(keyword.strip()) > 2:
                    enhanced_keywords.add(keyword.strip())
            
            # إضافة الكلمات المفتاحية الأساسية
            base_sample = random.sample(
                self.base_gaming_keywords, 
                min(5, len(self.base_gaming_keywords))
            )
            enhanced_keywords.update(base_sample)
            
            # إضافة كلمات مفتاحية حسب نوع المحتوى
            if content_type in self.content_type_keywords:
                type_keywords = random.sample(
                    self.content_type_keywords[content_type],
                    min(3, len(self.content_type_keywords[content_type]))
                )
                enhanced_keywords.update(type_keywords)
            
            # استخراج كلمات مفتاحية من المحتوى
            content_keywords = self._extract_keywords_from_content(content)
            enhanced_keywords.update(content_keywords[:3])
            
            # إضافة كلمات مفتاحية حسب المنصات المذكورة
            platform_keywords = self._detect_platform_keywords(content)
            enhanced_keywords.update(platform_keywords)
            
            # إضافة كلمات مفتاحية حسب النوع المكتشف
            genre_keywords = self._detect_genre_keywords(content)
            enhanced_keywords.update(genre_keywords)
            
            # تحويل إلى قائمة وترتيب حسب الأهمية
            final_keywords = list(enhanced_keywords)
            final_keywords = self._prioritize_keywords(final_keywords, content)
            
            # تحديد العدد الأقصى
            return final_keywords[:max_keywords]
            
        except Exception as e:
            logger.error("❌ فشل في تحسين الكلمات المفتاحية", e)
            return original_keywords[:max_keywords]
    
    def _extract_keywords_from_content(self, content: str) -> List[str]:
        """استخراج كلمات مفتاحية من المحتوى"""
        try:
            keywords = []
            
            # البحث عن أسماء الألعاب (بالإنجليزية)
            game_pattern = r'\b[A-Z][a-zA-Z\s:\']+(?:Game|Games|Gaming|Legends|Warfare|Craft|Quest|Adventure|Simulator|Edition)\b'
            games_found = re.findall(game_pattern, content)
            for game in games_found[:3]:
                if len(game.strip()) > 3:
                    keywords.append(game.strip())
            
            # البحث عن أسماء الشركات
            company_pattern = r'\b(?:Sony|Microsoft|Nintendo|Ubisoft|EA|Activision|Blizzard|Epic|Valve|Rockstar|Bethesda)\b'
            companies_found = re.findall(company_pattern, content)
            keywords.extend(companies_found[:2])
            
            # البحث عن المنصات
            platform_pattern = r'\b(?:PlayStation|Xbox|Nintendo Switch|Steam|PC|Mobile|iOS|Android)\b'
            platforms_found = re.findall(platform_pattern, content)
            keywords.extend(platforms_found[:2])
            
            return keywords
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في استخراج الكلمات المفتاحية: {e}")
            return []
    
    def _detect_platform_keywords(self, content: str) -> List[str]:
        """اكتشاف كلمات مفتاحية حسب المنصة"""
        try:
            keywords = []
            content_lower = content.lower()
            
            for platform, platform_keywords in self.platform_keywords.items():
                for keyword in platform_keywords:
                    if keyword.lower() in content_lower:
                        # إضافة كلمات مفتاحية عشوائية من هذه المنصة
                        sample_keywords = random.sample(
                            platform_keywords, 
                            min(2, len(platform_keywords))
                        )
                        keywords.extend(sample_keywords)
                        break
            
            return keywords
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في اكتشاف كلمات المنصة: {e}")
            return []
    
    def _detect_genre_keywords(self, content: str) -> List[str]:
        """اكتشاف كلمات مفتاحية حسب النوع"""
        try:
            keywords = []
            content_lower = content.lower()
            
            # كلمات دلالية لكل نوع
            genre_indicators = {
                'action': ['action', 'fight', 'battle', 'combat', 'shooter', 'أكشن', 'قتال', 'معركة'],
                'rpg': ['rpg', 'role', 'character', 'level', 'experience', 'تقمص', 'شخصية', 'مستوى'],
                'strategy': ['strategy', 'tactical', 'build', 'manage', 'استراتيجية', 'تكتيكي', 'إدارة'],
                'sports': ['sports', 'football', 'soccer', 'basketball', 'racing', 'رياضة', 'كرة', 'سباق'],
                'simulation': ['simulation', 'sim', 'life', 'city', 'farm', 'محاكاة', 'حياة', 'مدينة']
            }
            
            for genre, indicators in genre_indicators.items():
                for indicator in indicators:
                    if indicator in content_lower:
                        if genre in self.genre_keywords:
                            sample_keywords = random.sample(
                                self.genre_keywords[genre],
                                min(2, len(self.genre_keywords[genre]))
                            )
                            keywords.extend(sample_keywords)
                        break
            
            return keywords
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في اكتشاف كلمات النوع: {e}")
            return []
    
    def _prioritize_keywords(self, keywords: List[str], content: str) -> List[str]:
        """ترتيب الكلمات المفتاحية حسب الأهمية"""
        try:
            keyword_scores = {}
            content_lower = content.lower()
            
            for keyword in keywords:
                score = 0
                keyword_lower = keyword.lower()
                
                # نقاط حسب تكرار الكلمة في المحتوى
                score += content_lower.count(keyword_lower) * 2
                
                # نقاط إضافية للكلمات الإنجليزية (أهم للـ SEO)
                if re.search(r'[a-zA-Z]', keyword):
                    score += 3
                
                # نقاط إضافية للكلمات القصيرة (أسهل للبحث)
                if len(keyword) <= 15:
                    score += 1
                
                # نقاط إضافية للكلمات الأساسية
                if keyword in self.base_gaming_keywords:
                    score += 5
                
                keyword_scores[keyword] = score
            
            # ترتيب حسب النقاط
            sorted_keywords = sorted(
                keyword_scores.items(), 
                key=lambda x: x[1], 
                reverse=True
            )
            
            return [keyword for keyword, score in sorted_keywords]
            
        except Exception as e:
            logger.warning(f"⚠️ خطأ في ترتيب الكلمات المفتاحية: {e}")
            return keywords
    
    def format_keywords_section(self, keywords: List[str]) -> str:
        """تنسيق قسم الكلمات المفتاحية"""
        try:
            if not keywords:
                return ""
            
            # تنظيف وتحديد الكلمات المفتاحية
            cleaned_keywords = []
            for keyword in keywords[:15]:  # أقصى 15 كلمة
                if keyword and len(keyword.strip()) > 2:
                    cleaned_keywords.append(keyword.strip())
            
            if not cleaned_keywords:
                return ""
            
            keywords_text = ", ".join(cleaned_keywords)
            
            # تنسيق HTML جميل
            keywords_section = f"""

<div style="margin-top: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
    <div style="color: white; font-size: 16px; font-weight: bold; margin-bottom: 10px; display: flex; align-items: center;">
        <span style="margin-right: 8px;">🔍</span>
        الكلمات المفتاحية:
    </div>
    <div style="color: #f8f9fa; font-size: 14px; line-height: 1.6;">
        {keywords_text}
    </div>
</div>"""
            
            return keywords_section
            
        except Exception as e:
            logger.error("❌ فشل في تنسيق قسم الكلمات المفتاحية", e)
            return ""

# إنشاء كائن محسن الكلمات المفتاحية
keywords_enhancer = KeywordsEnhancer()
