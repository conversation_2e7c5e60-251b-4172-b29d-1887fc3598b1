#!/usr/bin/env python3
# اختبار مدير API المحسن

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_api_manager():
    """اختبار مدير API المحسن"""
    print("🔑 اختبار مدير API المحسن...")
    
    try:
        from modules.enhanced_api_manager import enhanced_api_manager
        
        # عرض إحصائيات عامة
        stats = enhanced_api_manager.get_usage_statistics()
        print(f"📊 إحصائيات عامة:")
        print(f"  • إجمالي الخدمات: {stats['total_services']}")
        print(f"  • إجمالي المفاتيح: {stats['total_keys']}")
        print(f"  • الخدمات النشطة: {stats['active_services']}")
        
        # عرض حالة الخدمات المهمة
        important_services = [
            'gemini', 'rawg', 'leap_ai', 'deepai', 'replicate', 
            'freepik', 'fluxai', 'telegram', 'serpapi'
        ]
        
        print(f"\n🔍 حالة الخدمات المهمة:")
        for service in important_services:
            status = enhanced_api_manager.get_service_status(service)
            status_icon = "✅" if status['status'] == 'active' else "❌" if status['status'] == 'inactive' else "⚠️"
            print(f"  {status_icon} {service}: {status['keys_count']} مفتاح ({status['active_keys']} نشط)")
        
        # اختبار الحصول على مفاتيح
        print(f"\n🧪 اختبار الحصول على المفاتيح:")
        
        test_services = ['gemini', 'rawg', 'serpapi', 'telegram']
        for service in test_services:
            key = enhanced_api_manager.get_api_key(service)
            if key:
                masked_key = f"{key[:8]}...{key[-8:]}" if len(key) > 16 else f"{key[:4]}..."
                print(f"  ✅ {service}: {masked_key}")
            else:
                print(f"  ❌ {service}: لا يوجد مفتاح متاح")
        
        # عرض تفاصيل جميع الخدمات
        print(f"\n📋 تفاصيل جميع الخدمات:")
        all_status = enhanced_api_manager.get_all_services_status()
        
        categories = {
            "🔍 خدمات البحث": ['google_enhanced', 'google_search', 'serpapi', 'tavily', 'search1api'],
            "🤖 الذكاء الاصطناعي": ['gemini', 'openai', 'claude'],
            "🎤 تحويل الصوت": ['assemblyai', 'speechmatics', 'ibm_watson', 'azure_speech', 'whisper'],
            "🖼️ الصور والوسائط": ['rawg', 'unsplash', 'pexels', 'pixabay', 'steam'],
            "🎨 توليد الصور": ['stability_ai', 'dalle', 'leap_ai', 'deepai', 'replicate', 'freepik', 'fluxai'],
            "📝 النشر": ['blogger_client_id', 'wordpress', 'medium', 'ghost_admin'],
            "📱 وسائل التواصل": ['telegram', 'twitter_api', 'facebook', 'instagram']
        }
        
        for category, services in categories.items():
            print(f"\n{category}:")
            for service in services:
                if service in all_status:
                    status = all_status[service]
                    status_icon = "✅" if status['status'] == 'active' else "❌" if status['status'] == 'inactive' else "⚠️"
                    print(f"  {status_icon} {service}: {status['keys_count']} مفتاح ({status['active_keys']} نشط) - استخدام: {status['total_usage']}")
                else:
                    print(f"  ⚪ {service}: غير مضبوط")
        
        # اختبار تدوير المفاتيح
        print(f"\n🔄 اختبار تدوير المفاتيح:")
        services_with_multiple_keys = [service for service, status in all_status.items() if status['keys_count'] > 1]
        
        if services_with_multiple_keys:
            test_service = services_with_multiple_keys[0]
            print(f"  🔄 تدوير مفاتيح {test_service}...")
            enhanced_api_manager.rotate_keys(test_service)
            print(f"  ✅ تم تدوير مفاتيح {test_service}")
        else:
            print(f"  ⚠️ لا توجد خدمات بمفاتيح متعددة للاختبار")
        
        # اختبار إعادة تعيين المفاتيح
        print(f"\n🔧 اختبار إعادة تعيين المفاتيح:")
        enhanced_api_manager.reset_error_keys()
        print(f"  ✅ تم فحص وإعادة تعيين المفاتيح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير API: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_key_security():
    """اختبار أمان مفاتيح API"""
    print("\n🔒 اختبار أمان مفاتيح API...")
    
    try:
        # فحص ملف .env للتأكد من عدم وجود مفاتيح افتراضية
        with open('.env', 'r', encoding='utf-8') as f:
            env_content = f.read()
        
        # البحث عن المفاتيح الافتراضية
        default_patterns = [
            'your_',
            'api_key_here',
            'token_here',
            'secret_here'
        ]
        
        issues_found = []
        lines = env_content.split('\n')
        
        for i, line in enumerate(lines, 1):
            if '=' in line and not line.strip().startswith('#'):
                key, value = line.split('=', 1)
                for pattern in default_patterns:
                    if pattern in value.lower():
                        issues_found.append(f"السطر {i}: {key} يحتوي على قيمة افتراضية")
        
        if issues_found:
            print(f"⚠️ تم العثور على {len(issues_found)} مفتاح بقيم افتراضية:")
            for issue in issues_found[:10]:  # عرض أول 10 فقط
                print(f"  • {issue}")
            if len(issues_found) > 10:
                print(f"  ... و {len(issues_found) - 10} مفتاح آخر")
        else:
            print(f"✅ جميع المفاتيح تبدو مضبوطة بقيم حقيقية")
        
        # فحص المفاتيح الحساسة
        sensitive_keys = [
            'GEMINI_API_KEY',
            'TELEGRAM_BOT_TOKEN',
            'BLOGGER_CLIENT_SECRET',
            'RAWG_API_KEY'
        ]
        
        print(f"\n🔍 فحص المفاتيح الحساسة:")
        for key in sensitive_keys:
            value = os.getenv(key)
            if value and not value.startswith('your_'):
                masked_value = f"{value[:4]}...{value[-4:]}" if len(value) > 8 else "***"
                print(f"  ✅ {key}: {masked_value}")
            else:
                print(f"  ❌ {key}: غير مضبوط أو قيمة افتراضية")
        
        return len(issues_found) == 0
        
    except Exception as e:
        print(f"❌ خطأ في فحص الأمان: {e}")
        return False

def generate_api_keys_report():
    """توليد تقرير شامل عن مفاتيح API"""
    print("\n📊 توليد تقرير مفاتيح API...")
    
    try:
        from modules.enhanced_api_manager import enhanced_api_manager
        
        # إنشاء تقرير
        report = {
            "timestamp": __import__('datetime').datetime.now().isoformat(),
            "summary": enhanced_api_manager.get_usage_statistics(),
            "services": enhanced_api_manager.get_all_services_status()
        }
        
        # حفظ التقرير
        import json
        with open('api_keys_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم حفظ التقرير في api_keys_report.json")
        
        # عرض ملخص
        summary = report['summary']
        print(f"\n📋 ملخص التقرير:")
        print(f"  • إجمالي الخدمات: {summary['total_services']}")
        print(f"  • إجمالي المفاتيح: {summary['total_keys']}")
        print(f"  • الخدمات النشطة: {summary['active_services']}")
        
        # أهم الخدمات النشطة
        active_services = [name for name, details in summary['services_detail'].items() 
                          if details['status'] == 'active']
        
        print(f"  • الخدمات النشطة: {', '.join(active_services[:10])}")
        if len(active_services) > 10:
            print(f"    ... و {len(active_services) - 10} خدمة أخرى")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في توليد التقرير: {e}")
        return False

def main():
    """تشغيل جميع اختبارات مدير API"""
    print("🧪 بدء اختبار مدير API المحسن")
    print("="*60)
    
    tests = [
        ("مدير API المحسن", test_enhanced_api_manager),
        ("أمان مفاتيح API", test_api_key_security),
        ("تقرير مفاتيح API", generate_api_keys_report)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔄 اختبار {test_name}...")
        print("-" * 40)
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                print(f"✅ نجح اختبار {test_name}")
            else:
                print(f"❌ فشل اختبار {test_name}")
                
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
        
        print("-" * 40)
    
    # النتائج النهائية
    print("\n📊 النتائج النهائية:")
    print("="*60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"  {status} - {test_name}")
    
    print(f"\n🎯 النتيجة الإجمالية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع اختبارات مدير API نجحت!")
    elif passed > total // 2:
        print("⚠️ معظم الاختبارات نجحت - يحتاج بعض التحسين")
    else:
        print("❌ يحتاج إصلاحات في مدير API")

if __name__ == "__main__":
    main()
