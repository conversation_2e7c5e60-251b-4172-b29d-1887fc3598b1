# -*- coding: utf-8 -*-
"""
اختبار شامل لـ APIs توليد الصور الجديدة
يختبر OpenArt، Leap AI، DeepAI، و Replicate
"""

import asyncio
import json
import time
from datetime import datetime
import sys
import os

# إضافة المسار للوحدات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.advanced_image_generator import advanced_image_generator
from config.new_image_apis_config import new_image_apis_config

class NewImageAPIsTestSuite:
    """مجموعة اختبارات شاملة للـ APIs الجديدة"""
    
    def __init__(self):
        self.test_results = {
            'test_timestamp': datetime.now().isoformat(),
            'overall_status': False,
            'apis_tested': 0,
            'apis_working': 0,
            'individual_tests': {},
            'performance_metrics': {},
            'recommendations': []
        }
        
        # prompts اختبار متنوعة
        self.test_prompts = [
            {
                'prompt': 'A futuristic gaming controller with RGB lighting',
                'category': 'gaming',
                'expected_elements': ['controller', 'gaming', 'RGB', 'futuristic']
            },
            {
                'prompt': 'Epic fantasy game character with magical sword',
                'category': 'gaming',
                'expected_elements': ['character', 'fantasy', 'sword', 'magical']
            },
            {
                'prompt': 'Modern gaming setup with multiple monitors',
                'category': 'gaming',
                'expected_elements': ['gaming', 'setup', 'monitors', 'modern']
            }
        ]
    
    async def run_comprehensive_test(self):
        """تشغيل اختبار شامل لجميع APIs"""
        print("🧪 بدء الاختبار الشامل لـ APIs توليد الصور الجديدة")
        print("=" * 60)
        
        # اختبار التكوين
        await self._test_configuration()
        
        # اختبار كل API على حدة
        await self._test_individual_apis()
        
        # اختبار النظام المتكامل
        await self._test_integrated_system()
        
        # اختبار الأداء
        await self._test_performance()
        
        # إنشاء التقرير النهائي
        await self._generate_final_report()
        
        return self.test_results
    
    async def _test_configuration(self):
        """اختبار التكوين والإعدادات"""
        print("\n📋 اختبار التكوين والإعدادات...")
        
        config_test = {
            'config_loaded': False,
            'apis_configured': 0,
            'apis_enabled': 0,
            'missing_keys': []
        }
        
        try:
            # فحص تحميل التكوين
            if new_image_apis_config:
                config_test['config_loaded'] = True
                print("✅ تم تحميل التكوين بنجاح")
            
            # فحص APIs المكونة
            for api_name, api_config in new_image_apis_config.apis.items():
                config_test['apis_configured'] += 1
                
                if api_config.enabled and api_config.api_key:
                    config_test['apis_enabled'] += 1
                    print(f"✅ {api_config.name}: مكون ومفعل")
                elif api_config.enabled and not api_config.api_key:
                    config_test['missing_keys'].append(api_name)
                    print(f"⚠️ {api_config.name}: مفعل لكن مفتاح API مفقود")
                else:
                    print(f"❌ {api_config.name}: غير مفعل")
            
            self.test_results['individual_tests']['configuration'] = config_test
            
        except Exception as e:
            print(f"❌ خطأ في اختبار التكوين: {e}")
            config_test['error'] = str(e)
    
    async def _test_individual_apis(self):
        """اختبار كل API على حدة"""
        print("\n🔍 اختبار APIs الفردية...")
        
        for api_name, api_config in new_image_apis_config.apis.items():
            if not api_config.enabled or not api_config.api_key:
                print(f"⏭️ تخطي {api_config.name} (غير مفعل أو مفتاح مفقود)")
                continue
            
            print(f"\n🧪 اختبار {api_config.name}...")
            
            api_test_result = {
                'api_name': api_config.name,
                'enabled': api_config.enabled,
                'has_key': bool(api_config.api_key),
                'tests_passed': 0,
                'tests_failed': 0,
                'average_response_time': 0,
                'errors': []
            }
            
            response_times = []
            
            # اختبار عدة prompts
            for i, test_prompt in enumerate(self.test_prompts):
                try:
                    print(f"  📝 اختبار {i+1}: {test_prompt['prompt'][:30]}...")
                    
                    start_time = time.time()
                    result = await advanced_image_generator._generate_with_api(
                        api_name, 
                        test_prompt['prompt']
                    )
                    response_time = time.time() - start_time
                    response_times.append(response_time)
                    
                    if result and result.get('url'):
                        api_test_result['tests_passed'] += 1
                        print(f"    ✅ نجح في {response_time:.2f}s")
                        print(f"    🔗 الرابط: {result['url'][:50]}...")
                    else:
                        api_test_result['tests_failed'] += 1
                        print(f"    ❌ فشل - لم يتم إرجاع صورة صالحة")
                        
                except Exception as e:
                    api_test_result['tests_failed'] += 1
                    api_test_result['errors'].append(str(e))
                    print(f"    ❌ خطأ: {e}")
                
                # انتظار قصير بين الاختبارات
                await asyncio.sleep(2)
            
            # حساب متوسط وقت الاستجابة
            if response_times:
                api_test_result['average_response_time'] = sum(response_times) / len(response_times)
            
            self.test_results['individual_tests'][api_name] = api_test_result
            self.test_results['apis_tested'] += 1
            
            if api_test_result['tests_passed'] > 0:
                self.test_results['apis_working'] += 1
                print(f"✅ {api_config.name}: {api_test_result['tests_passed']}/{len(self.test_prompts)} اختبارات نجحت")
            else:
                print(f"❌ {api_config.name}: فشل في جميع الاختبارات")
    
    async def _test_integrated_system(self):
        """اختبار النظام المتكامل"""
        print("\n🔗 اختبار النظام المتكامل...")
        
        integrated_test = {
            'total_tests': 0,
            'successful_generations': 0,
            'fallback_used': 0,
            'cache_hits': 0,
            'average_response_time': 0
        }
        
        response_times = []
        
        for i, test_prompt in enumerate(self.test_prompts):
            try:
                print(f"🧪 اختبار متكامل {i+1}: {test_prompt['prompt'][:40]}...")
                
                start_time = time.time()
                result = await advanced_image_generator.generate_image(
                    test_prompt['prompt'],
                    {'title': f'Test Article {i+1}', 'category': test_prompt['category']}
                )
                response_time = time.time() - start_time
                response_times.append(response_time)
                
                integrated_test['total_tests'] += 1
                
                if result and result.get('url'):
                    integrated_test['successful_generations'] += 1
                    
                    if result.get('fallback_system'):
                        integrated_test['fallback_used'] += 1
                        print(f"  ✅ نجح باستخدام النظام الاحتياطي ({response_time:.2f}s)")
                    else:
                        print(f"  ✅ نجح باستخدام النظام الجديد ({response_time:.2f}s)")
                        print(f"  🎨 API المستخدم: {result.get('api_used', 'غير محدد')}")
                else:
                    print(f"  ❌ فشل في إنشاء صورة")
                
            except Exception as e:
                print(f"  ❌ خطأ في الاختبار المتكامل: {e}")
            
            await asyncio.sleep(3)
        
        if response_times:
            integrated_test['average_response_time'] = sum(response_times) / len(response_times)
        
        self.test_results['individual_tests']['integrated_system'] = integrated_test
    
    async def _test_performance(self):
        """اختبار الأداء والسرعة"""
        print("\n⚡ اختبار الأداء...")
        
        # الحصول على إحصائيات الاستخدام
        usage_stats = advanced_image_generator.get_usage_stats()
        
        performance_metrics = {
            'total_requests': usage_stats['total_requests'],
            'success_rate': usage_stats['success_rate'],
            'cache_size': usage_stats['cache_size'],
            'fastest_api': None,
            'slowest_api': None,
            'most_reliable_api': None
        }
        
        # تحليل أداء APIs
        api_performance = {}
        for api_name, test_result in self.test_results['individual_tests'].items():
            if api_name in ['configuration', 'integrated_system']:
                continue
                
            if 'average_response_time' in test_result and test_result['average_response_time'] > 0:
                success_rate = (
                    test_result['tests_passed'] / 
                    (test_result['tests_passed'] + test_result['tests_failed']) * 100
                    if (test_result['tests_passed'] + test_result['tests_failed']) > 0 else 0
                )
                
                api_performance[api_name] = {
                    'response_time': test_result['average_response_time'],
                    'success_rate': success_rate
                }
        
        # تحديد أسرع وأبطأ API
        if api_performance:
            fastest = min(api_performance.items(), key=lambda x: x[1]['response_time'])
            slowest = max(api_performance.items(), key=lambda x: x[1]['response_time'])
            most_reliable = max(api_performance.items(), key=lambda x: x[1]['success_rate'])
            
            performance_metrics['fastest_api'] = {
                'name': fastest[0],
                'response_time': fastest[1]['response_time']
            }
            performance_metrics['slowest_api'] = {
                'name': slowest[0],
                'response_time': slowest[1]['response_time']
            }
            performance_metrics['most_reliable_api'] = {
                'name': most_reliable[0],
                'success_rate': most_reliable[1]['success_rate']
            }
        
        self.test_results['performance_metrics'] = performance_metrics
        
        print(f"📊 إجمالي الطلبات: {performance_metrics['total_requests']}")
        print(f"📈 معدل النجاح: {performance_metrics['success_rate']:.1f}%")
        if performance_metrics['fastest_api']:
            print(f"🚀 أسرع API: {performance_metrics['fastest_api']['name']} ({performance_metrics['fastest_api']['response_time']:.2f}s)")
        if performance_metrics['most_reliable_api']:
            print(f"🎯 أكثر API موثوقية: {performance_metrics['most_reliable_api']['name']} ({performance_metrics['most_reliable_api']['success_rate']:.1f}%)")
    
    async def _generate_final_report(self):
        """إنشاء التقرير النهائي"""
        print("\n📋 إنشاء التقرير النهائي...")
        
        # تحديد الحالة العامة
        self.test_results['overall_status'] = self.test_results['apis_working'] > 0
        
        # إنشاء التوصيات
        recommendations = []
        
        if self.test_results['apis_working'] == 0:
            recommendations.append("❌ لا توجد APIs تعمل - تحقق من مفاتيح API")
        elif self.test_results['apis_working'] < self.test_results['apis_tested']:
            recommendations.append("⚠️ بعض APIs لا تعمل - تحقق من المفاتيح والإعدادات")
        else:
            recommendations.append("✅ جميع APIs تعمل بشكل صحيح")
        
        # توصيات الأداء
        if 'fastest_api' in self.test_results['performance_metrics'] and self.test_results['performance_metrics']['fastest_api']:
            fastest_api = self.test_results['performance_metrics']['fastest_api']['name']
            recommendations.append(f"🚀 للحصول على أفضل سرعة، استخدم {fastest_api}")
        
        if 'most_reliable_api' in self.test_results['performance_metrics'] and self.test_results['performance_metrics']['most_reliable_api']:
            reliable_api = self.test_results['performance_metrics']['most_reliable_api']['name']
            recommendations.append(f"🎯 للحصول على أفضل موثوقية، استخدم {reliable_api}")
        
        self.test_results['recommendations'] = recommendations
        
        # حفظ التقرير
        report_file = f"test_results/new_image_apis_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"💾 تم حفظ التقرير في: {report_file}")

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🎨 اختبار شامل لـ APIs توليد الصور الجديدة")
    print("=" * 60)
    
    test_suite = NewImageAPIsTestSuite()
    results = await test_suite.run_comprehensive_test()
    
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج:")
    print(f"APIs تم اختبارها: {results['apis_tested']}")
    print(f"APIs تعمل: {results['apis_working']}")
    print(f"الحالة العامة: {'✅ نجح' if results['overall_status'] else '❌ فشل'}")
    
    print("\n🎯 التوصيات:")
    for recommendation in results['recommendations']:
        print(f"  {recommendation}")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
