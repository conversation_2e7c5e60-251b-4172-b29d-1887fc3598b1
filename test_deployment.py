#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للوكيل قبل النشر
Comprehensive Agent Testing Before Deployment
"""

import os
import sys
import time
import requests
import subprocess
import threading
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class DeploymentTester:
    """فئة اختبار النشر"""
    
    def __init__(self):
        self.test_results = {}
        self.web_server_process = None
        
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء الاختبار الشامل للوكيل")
        print("=" * 60)
        
        # اختبارات الملفات الأساسية
        self.test_required_files()
        
        # اختبارات المتطلبات
        self.test_requirements()
        
        # اختبارات الوحدات
        self.test_modules_import()
        
        # اختبارات قاعدة البيانات
        self.test_database()
        
        # اختبارات الواجهة الويب
        self.test_web_interface()
        
        # اختبارات التكوين
        self.test_configuration()
        
        # عرض النتائج
        self.display_results()
        
        return all(self.test_results.values())
    
    def test_required_files(self):
        """اختبار الملفات المطلوبة"""
        print("\n📁 اختبار الملفات المطلوبة...")
        
        required_files = [
            'main.py',
            'web_api.py',
            'deployment_config.py',
            'requirements.txt',
            'Procfile',
            'render.yaml'
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
                print(f"   ❌ {file} - مفقود")
            else:
                print(f"   ✅ {file}")
        
        self.test_results['required_files'] = len(missing_files) == 0
        
        if missing_files:
            print(f"⚠️ ملفات مفقودة: {', '.join(missing_files)}")
        else:
            print("✅ جميع الملفات المطلوبة موجودة")
    
    def test_requirements(self):
        """اختبار المتطلبات"""
        print("\n📦 اختبار المتطلبات...")
        
        try:
            with open('requirements.txt', 'r', encoding='utf-8') as f:
                requirements = f.read()
            
            # فحص المتطلبات الأساسية
            essential_packages = [
                'flask',
                'flask-cors',
                'google-generativeai',
                'requests',
                'beautifulsoup4'
            ]
            
            missing_packages = []
            for package in essential_packages:
                if package not in requirements.lower():
                    missing_packages.append(package)
                    print(f"   ❌ {package} - مفقود من requirements.txt")
                else:
                    print(f"   ✅ {package}")
            
            self.test_results['requirements'] = len(missing_packages) == 0
            
            if missing_packages:
                print(f"⚠️ حزم مفقودة: {', '.join(missing_packages)}")
            else:
                print("✅ جميع المتطلبات الأساسية موجودة")
                
        except Exception as e:
            print(f"❌ خطأ في قراءة requirements.txt: {e}")
            self.test_results['requirements'] = False
    
    def test_modules_import(self):
        """اختبار استيراد الوحدات"""
        print("\n🔧 اختبار استيراد الوحدات...")
        
        modules_to_test = [
            ('deployment_config', 'deployment_config'),
            ('web_api', 'web_api'),
            ('modules.logger', 'logger'),
            ('modules.database', 'db')
        ]
        
        failed_imports = []
        for module_name, import_name in modules_to_test:
            try:
                __import__(module_name)
                print(f"   ✅ {module_name}")
            except ImportError as e:
                failed_imports.append(module_name)
                print(f"   ❌ {module_name} - {e}")
        
        self.test_results['modules_import'] = len(failed_imports) == 0
        
        if failed_imports:
            print(f"⚠️ فشل في استيراد: {', '.join(failed_imports)}")
        else:
            print("✅ تم استيراد جميع الوحدات بنجاح")
    
    def test_database(self):
        """اختبار قاعدة البيانات"""
        print("\n🗄️ اختبار قاعدة البيانات...")
        
        try:
            # إنشاء مجلد البيانات
            os.makedirs('data', exist_ok=True)
            
            # اختبار إنشاء قاعدة البيانات
            import sqlite3
            conn = sqlite3.connect('data/test.db')
            cursor = conn.cursor()
            
            # إنشاء جدول اختبار
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS test_table (
                    id INTEGER PRIMARY KEY,
                    name TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إدراج بيانات اختبار
            cursor.execute("INSERT INTO test_table (name) VALUES (?)", ("test",))
            conn.commit()
            
            # قراءة البيانات
            cursor.execute("SELECT * FROM test_table")
            result = cursor.fetchone()
            
            conn.close()
            
            # حذف قاعدة البيانات التجريبية
            os.remove('data/test.db')
            
            self.test_results['database'] = result is not None
            print("✅ قاعدة البيانات تعمل بشكل صحيح")
            
        except Exception as e:
            print(f"❌ خطأ في قاعدة البيانات: {e}")
            self.test_results['database'] = False
    
    def test_web_interface(self):
        """اختبار الواجهة الويب"""
        print("\n🌐 اختبار الواجهة الويب...")
        
        try:
            # فحص ملفات الواجهة
            web_files = [
                'web_interface/index.html',
                'web_interface/styles.css',
                'web_interface/script.js'
            ]
            
            missing_web_files = []
            for file in web_files:
                if not os.path.exists(file):
                    missing_web_files.append(file)
                    print(f"   ❌ {file} - مفقود")
                else:
                    print(f"   ✅ {file}")
            
            # اختبار استيراد web_api
            try:
                import web_api
                print("   ✅ web_api.py يمكن استيراده")
                web_api_import = True
            except Exception as e:
                print(f"   ❌ web_api.py - خطأ في الاستيراد: {e}")
                web_api_import = False
            
            self.test_results['web_interface'] = len(missing_web_files) == 0 and web_api_import
            
            if missing_web_files:
                print(f"⚠️ ملفات واجهة مفقودة: {', '.join(missing_web_files)}")
            elif not web_api_import:
                print("⚠️ مشكلة في استيراد web_api")
            else:
                print("✅ الواجهة الويب جاهزة")
                
        except Exception as e:
            print(f"❌ خطأ في اختبار الواجهة الويب: {e}")
            self.test_results['web_interface'] = False
    
    def test_configuration(self):
        """اختبار التكوين"""
        print("\n⚙️ اختبار التكوين...")
        
        try:
            # اختبار deployment_config
            from deployment_config import deployment_config
            
            platform = deployment_config.platform
            print(f"   ✅ المنصة المكتشفة: {platform}")
            
            # اختبار إنشاء المجلدات
            deployment_config.create_required_directories()
            print("   ✅ تم إنشاء المجلدات المطلوبة")
            
            # اختبار الإعدادات
            db_url = deployment_config.get_database_url()
            security_config = deployment_config.get_security_config()
            
            print(f"   ✅ رابط قاعدة البيانات: {db_url}")
            print(f"   ✅ إعدادات الأمان: محددة")
            
            self.test_results['configuration'] = True
            print("✅ التكوين صحيح")
            
        except Exception as e:
            print(f"❌ خطأ في التكوين: {e}")
            self.test_results['configuration'] = False
    
    def display_results(self):
        """عرض نتائج الاختبارات"""
        print("\n" + "=" * 60)
        print("📊 نتائج الاختبارات")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        
        for test_name, result in self.test_results.items():
            status = "✅ نجح" if result else "❌ فشل"
            print(f"{test_name}: {status}")
        
        print("-" * 60)
        print(f"المجموع: {passed_tests}/{total_tests} اختبار نجح")
        
        if passed_tests == total_tests:
            print("🎉 جميع الاختبارات نجحت! الوكيل جاهز للنشر")
        else:
            print("⚠️ بعض الاختبارات فشلت. يرجى إصلاح المشاكل قبل النشر")
        
        return passed_tests == total_tests

def main():
    """الدالة الرئيسية"""
    tester = DeploymentTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🚀 الوكيل جاهز للنشر!")
        print("يمكنك الآن نشره على المنصة المفضلة لديك")
    else:
        print("\n❌ يرجى إصلاح المشاكل المذكورة أعلاه قبل النشر")
    
    return success

if __name__ == "__main__":
    main()
