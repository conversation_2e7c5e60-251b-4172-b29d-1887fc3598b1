# -*- coding: utf-8 -*-
"""
اختبار سريع للنظام الجديد لتوليد الصور
"""

import asyncio
import sys
import os

# إضافة المسار للوحدات
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def quick_test():
    """اختبار سريع للنظام الجديد"""
    print("🎨 اختبار سريع لنظام توليد الصور الجديد")
    print("=" * 50)
    
    try:
        # اختبار التكوين
        print("\n📋 فحص التكوين...")
        from config.new_image_apis_config import new_image_apis_config
        
        enabled_apis = new_image_apis_config.get_enabled_apis()
        print(f"✅ تم تحميل التكوين - {len(enabled_apis)} APIs مفعلة")
        
        for api in enabled_apis:
            status = "🟢" if api.api_key else "🔴"
            print(f"  {status} {api.name}: {'مفتاح متوفر' if api.api_key else 'مفتاح مفقود'}")
        
        # اختبار المولد
        print("\n🎨 فحص مولد الصور...")
        from modules.advanced_image_generator import advanced_image_generator
        print("✅ تم تحميل مولد الصور المتقدم")
        
        # اختبار بسيط
        print("\n🧪 اختبار إنشاء صورة...")
        result = await advanced_image_generator.generate_image(
            "A simple gaming controller, digital art style",
            {'title': 'Test Article', 'category': 'gaming'}
        )
        
        if result:
            print("✅ تم إنشاء صورة بنجاح!")
            print(f"🔗 الرابط: {result['url']}")
            print(f"🎯 API المستخدم: {result.get('api_used', 'غير محدد')}")
            print(f"📊 طريقة الإنشاء: {result.get('generation_method', 'غير محدد')}")
            
            if result.get('fallback_system'):
                print("⚠️ تم استخدام النظام الاحتياطي")
            elif result.get('fallback_image'):
                print("⚠️ تم استخدام صورة placeholder")
        else:
            print("❌ فشل في إنشاء الصورة")
        
        # عرض الإحصائيات
        print("\n📊 الإحصائيات:")
        stats = advanced_image_generator.get_usage_stats()
        print(f"إجمالي الطلبات: {stats['total_requests']}")
        print(f"معدل النجاح: {stats['success_rate']:.1f}%")
        print(f"حجم التخزين المؤقت: {stats['cache_size']}")
        
        # اختبار التكامل مع النظام الموجود
        print("\n🔗 اختبار التكامل مع النظام الموجود...")
        try:
            from modules.smart_image_manager import smart_image_manager
            
            test_article = {
                'title': 'اختبار التكامل',
                'content': 'محتوى اختبار للتكامل مع النظام الجديد',
                'image_prompts': ['gaming setup with RGB lighting']
            }
            
            integration_result = await smart_image_manager.generate_smart_image_for_article(test_article)
            
            if integration_result:
                print("✅ التكامل يعمل بنجاح!")
                print(f"🎯 API المستخدم: {integration_result.get('api_used', 'غير محدد')}")
            else:
                print("⚠️ التكامل لم ينجح، لكن هذا طبيعي إذا لم تكن APIs مكونة")
                
        except Exception as e:
            print(f"⚠️ خطأ في اختبار التكامل: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 انتهى الاختبار السريع!")
        
        if len(enabled_apis) == 0:
            print("\n💡 نصيحة: أضف مفاتيح API في ملف .env لتفعيل الخدمات الجديدة")
            print("📖 راجع ملف NEW_IMAGE_APIS_GUIDE.md للتفاصيل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(quick_test())
