@echo off
chcp 65001 >nul
title رفع وكيل أخبار الألعاب إلى GitHub - vandal324
color 0A

echo ========================================
echo 🚀 رفع وكيل أخبار الألعاب إلى GitHub
echo 👤 الحساب: vandal324
echo 📁 المستودع: gaming-news-agent
echo ========================================
echo.

REM التحقق من وجود Git
git --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Git غير مثبت. يرجى تثبيت Git أولاً من: https://git-scm.com/
    pause
    exit /b 1
)

echo ✅ Git مثبت ومتاح
echo.

REM إعداد Git
echo 🔧 إعداد Git للحساب vandal324...
git config user.name "vandal324"
git config user.email "<EMAIL>"
echo ✅ تم إعداد Git

echo.
echo 📁 إعداد المستودع المحلي...

REM تهيئة Git إذا لم يكن مهيئاً
if not exist ".git" (
    git init
    echo ✅ تم تهيئة مستودع Git محلي
) else (
    echo ✅ مستودع Git موجود بالفعل
)

echo.
echo 📝 إضافة الملفات الأساسية...

REM إضافة الملفات الأساسية
git add main.py web_api.py deployment_config.py requirements.txt README.md LICENSE Procfile render.yaml .gitignore
if errorlevel 1 (
    echo ❌ فشل في إضافة الملفات الأساسية
    pause
    exit /b 1
)
echo ✅ تم إضافة الملفات الأساسية

echo.
echo 📂 إضافة المجلدات...

REM إضافة المجلدات المطلوبة
git add modules/ config/ web_interface/
if errorlevel 1 (
    echo ❌ فشل في إضافة المجلدات
    pause
    exit /b 1
)
echo ✅ تم إضافة المجلدات

echo.
echo 📚 إضافة ملفات التوثيق...

REM إضافة ملفات التوثيق
git add DEPLOYMENT_GUIDE.md README_DEPLOYMENT.md test_deployment.py quick_start.py MANUAL_GITHUB_UPLOAD.md
echo ✅ تم إضافة ملفات التوثيق

echo.
echo 💾 إنشاء commit...
git commit -m "🎮 Gaming News Agent - Ready for deployment

✨ Features:
- Smart news collection with AI
- Interactive web interface  
- Ready for hosting (Render, Heroku, Railway)
- Real-time monitoring and control
- Secure API key management

🚀 Ready to deploy on multiple platforms
🌐 Web interface at localhost:5000
📖 Complete deployment guide included

👤 Account: vandal324
📧 Email: <EMAIL>"

if errorlevel 1 (
    echo ⚠️ لا توجد تغييرات جديدة للـ commit أو تم إنشاء commit بالفعل
) else (
    echo ✅ تم إنشاء commit بنجاح
)

echo.
echo 🌐 إضافة المستودع البعيد...

REM إزالة المستودع البعيد إذا كان موجوداً
git remote remove origin 2>nul

REM إضافة المستودع البعيد الجديد
git remote add origin https://github.com/vandal324/gaming-news-agent.git
if errorlevel 1 (
    echo ❌ فشل في إضافة المستودع البعيد
    pause
    exit /b 1
)
echo ✅ تم إضافة المستودع البعيد

echo.
echo 🔄 إعداد branch main...
git branch -M main
echo ✅ تم إعداد branch main

echo.
echo 📤 رفع الملفات إلى GitHub...
echo ⚠️ ستحتاج إلى إدخال معلومات المصادقة:
echo 👤 Username: vandal324
echo 🔑 Password: استخدم Personal Access Token
echo 🔗 إنشاء Token: https://github.com/settings/tokens
echo.

REM رفع الملفات
git push -u origin main

if errorlevel 1 (
    echo.
    echo ❌ فشل في رفع الملفات
    echo.
    echo 💡 نصائح لحل المشكلة:
    echo 1. تأكد من إنشاء المستودع على GitHub أولاً:
    echo    https://github.com/new
    echo    Repository name: gaming-news-agent
    echo    Owner: vandal324
    echo    Private: ✅ نعم
    echo.
    echo 2. تأكد من صحة معلومات المصادقة:
    echo    Username: vandal324
    echo    Password: Personal Access Token (ليس كلمة المرور العادية)
    echo.
    echo 3. لإنشاء Personal Access Token:
    echo    - اذهب إلى: https://github.com/settings/tokens
    echo    - اضغط "Generate new token (classic)"
    echo    - اختر Scopes: repo, workflow
    echo    - انسخ التوكن واستخدمه كـ password
    echo.
    echo 4. تأكد من أن لديك صلاحيات الكتابة على المستودع
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo 🎉 تم رفع المشروع بنجاح!
    echo.
    echo 🔗 رابط المشروع: https://github.com/vandal324/gaming-news-agent
    echo.
    echo 📋 الخطوات التالية:
    echo 1. ✅ اذهب إلى GitHub وتأكد من رفع الملفات
    echo 2. ✅ أضف وصف للمشروع
    echo 3. ✅ أضف Topics: python, flask, ai, gaming, news
    echo 4. ✅ تأكد من أن المشروع Private
    echo.
    echo 🚀 للنشر على Render:
    echo 1. اذهب إلى: https://render.com
    echo 2. أنشئ Web Service جديد
    echo 3. اربط مستودع GitHub: vandal324/gaming-news-agent
    echo 4. أضف متغيرات البيئة (مفاتيح API)
    echo 5. انشر!
    echo.
    echo 🌐 بعد النشر ستحصل على رابط مثل:
    echo https://gaming-news-agent.onrender.com
    echo.
)

pause
