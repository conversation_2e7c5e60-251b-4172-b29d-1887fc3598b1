{"name": "Gaming News Agent", "version": "1.0.0", "description": "وكيل أخبار الألعاب الذكي مع واجهة ويب تفاعلية", "author": "Mcamento8", "repository": "https://github.com/Mcamento8/gaming-news-agent", "license": "MIT", "python_version": "3.8+", "main_file": "main.py", "web_interface": "http://localhost:5000", "deployment_platforms": ["Render", "Heroku", "Railway"], "features": ["Smart news collection with AI", "Interactive web interface", "Real-time monitoring", "Secure API management", "Multi-platform deployment"], "created_date": "2025-07-24T11:55:10.260746", "last_updated": "2025-07-24T11:55:10.260799"}