# 🎮 وكيل أخبار الألعاب - جاهز للنشر

## 📋 نظرة عامة

وكيل برمجي ذكي لجمع ونشر أخبار الألعاب تلقائياً مع واجهة ويب تفاعلية للمراقبة والتحكم.

## ✨ المميزات الرئيسية

### 🤖 الوكيل الذكي
- جمع أخبار الألعاب من مصادر متعددة
- تحليل المحتوى باستخدام الذكاء الاصطناعي
- إنشاء مقالات عالية الجودة
- نشر تلقائي على Blogger

### 🌐 واجهة الويب
- لوحة تحكم تفاعلية
- مراقبة الحالة في الوقت الفعلي
- إدارة الإعدادات
- عرض الإحصائيات والسجلات

### 🚀 جاهز للنشر
- متوافق مع Render, Heroku, Railway
- إعدادات تلقائية للاستضافة
- نظام منع النوم المدمج
- مراقبة الصحة

## 🛠️ التقنيات المستخدمة

- **Python 3.8+**
- **Flask** - خادم الويب
- **Google Gemini AI** - الذكاء الاصطناعي
- **SQLite** - قاعدة البيانات
- **HTML/CSS/JavaScript** - الواجهة الأمامية

## 📁 هيكل المشروع

```
gaming-news-agent/
├── main.py                 # الملف الرئيسي
├── web_api.py             # خادم الواجهة الويب
├── deployment_config.py   # إعدادات النشر
├── requirements.txt       # متطلبات Python
├── Procfile              # ملف Heroku
├── render.yaml           # ملف Render
├── modules/              # وحدات الوكيل
│   ├── logger.py
│   ├── database.py
│   ├── content_scraper.py
│   └── ...
├── config/               # ملفات التكوين
├── web_interface/        # ملفات الواجهة الويب
│   ├── index.html
│   ├── styles.css
│   └── script.js
└── data/                # قاعدة البيانات (تُنشأ تلقائياً)
```

## 🚀 التشغيل السريع

### 1. التشغيل المحلي
```bash
# استنساخ المشروع
git clone https://github.com/username/gaming-news-agent.git
cd gaming-news-agent

# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل الوكيل
python main.py
```

### 2. الوصول للواجهة
افتح المتصفح على: `http://localhost:5000`

## 🌐 النشر على الاستضافة

### Render (الأفضل - مجاني)
1. ارفع الكود إلى GitHub
2. اذهب إلى [render.com](https://render.com)
3. أنشئ Web Service جديد
4. اربط مستودع GitHub
5. أضف متغيرات البيئة
6. انشر!

### Heroku
```bash
heroku create gaming-news-agent
heroku config:set GEMINI_API_KEY=your_key
git push heroku main
```

### Railway
1. اذهب إلى [railway.app](https://railway.app)
2. اربط GitHub
3. أضف متغيرات البيئة
4. انشر

## ⚙️ متغيرات البيئة المطلوبة

```env
# مفاتيح API الأساسية
GEMINI_API_KEY=your_gemini_api_key
TELEGRAM_BOT_TOKEN=your_telegram_token
BLOGGER_CLIENT_ID=your_blogger_client_id
BLOGGER_CLIENT_SECRET=your_blogger_client_secret

# مفاتيح إضافية (اختيارية)
OPENART_API_KEY=your_openart_key
LEAP_AI_API_KEY=your_leap_ai_key
DEEPAI_API_KEY=your_deepai_key
REPLICATE_API_TOKEN=your_replicate_token
NEWSDATA_API_KEY=your_newsdata_key
SERPAPI_KEY=your_serpapi_key
TAVILY_API_KEY=your_tavily_key
```

## 🧪 الاختبار

```bash
# اختبار شامل قبل النشر
python test_deployment.py

# اختبار الوحدات
python -m pytest tests/

# اختبار الواجهة الويب
python test_web_system.py
```

## 📊 المراقبة

### لوحة التحكم
- **الحالة**: مراقبة حالة الوكيل
- **الإحصائيات**: عدد المقالات المنشورة
- **السجلات**: عرض آخر الأحداث
- **الإعدادات**: تخصيص سلوك الوكيل

### نقاط API
- `GET /health` - فحص صحة النظام
- `GET /api/status` - حالة الوكيل
- `GET /api/logs` - السجلات
- `POST /api/settings` - تحديث الإعدادات

## 🔧 التخصيص

### إضافة مصادر جديدة
```python
# في modules/content_scraper.py
def add_new_source(self, source_config):
    # إضافة مصدر جديد
    pass
```

### تخصيص الواجهة
```css
/* في web_interface/styles.css */
.custom-theme {
    /* تخصيص المظهر */
}
```

## 🛡️ الأمان

- **مفاتيح API**: محفوظة في متغيرات البيئة
- **HTTPS**: مفعل تلقائياً على المنصات السحابية
- **CORS**: محدود للأمان
- **Rate Limiting**: حماية من الطلبات المفرطة

## 📈 الأداء

- **استخدام الذاكرة**: ~200-400MB
- **زمن الاستجابة**: <2 ثانية
- **معدل النجاح**: >95%
- **وقت التشغيل**: 24/7

## 🆘 استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في مفاتيح API**: تأكد من صحة المفاتيح
2. **مشكلة في قاعدة البيانات**: فحص أذونات الكتابة
3. **خطأ في الواجهة**: مراجعة ملفات web_interface
4. **مشكلة في النشر**: راجع السجلات

### الحصول على المساعدة
- راجع ملف `DEPLOYMENT_GUIDE.md`
- فحص السجلات في `/api/logs`
- تشغيل `test_deployment.py`

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد
3. إضافة التحسينات
4. إرسال Pull Request

## 📞 التواصل

- **GitHub**: [رابط المشروع]
- **البريد الإلكتروني**: [بريدك الإلكتروني]
- **التوثيق**: راجع ملفات .md في المشروع

---

## 🎉 شكراً لاستخدام وكيل أخبار الألعاب!

إذا أعجبك المشروع، لا تنس إعطاؤه ⭐ على GitHub!
