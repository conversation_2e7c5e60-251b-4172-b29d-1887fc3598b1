#!/usr/bin/env python3
"""
اختبار شامل للتحسينات الجديدة على الوكيل
"""

import asyncio
import json
import os
from datetime import datetime
from typing import Dict, List

# إضافة المسار الحالي
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.internal_links_manager import internal_links_manager
from modules.licensed_image_manager import licensed_image_manager
from modules.general_article_image_generator import general_article_image_generator
from modules.smart_image_compositor import smart_image_compositor
from modules.smart_image_manager import smart_image_manager


class EnhancedAgentTester:
    """فئة اختبار التحسينات الجديدة"""
    
    def __init__(self):
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {
                'total_tests': 0,
                'passed': 0,
                'failed': 0,
                'warnings': 0
            }
        }
        
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        logger.info("🧪 بدء اختبار التحسينات الجديدة للوكيل...")
        
        # اختبار نظام الروابط المحسن
        await self._test_enhanced_links_system()
        
        # اختبار نظام البحث عن الصور المحسن
        await self._test_enhanced_image_search()
        
        # اختبار نظام إنشاء صور المقالات العامة
        await self._test_general_article_images()
        
        # اختبار نظام التركيب الذكي للصور
        await self._test_smart_image_composition()
        
        # اختبار التكامل الشامل
        await self._test_full_integration()
        
        # إنشاء التقرير النهائي
        self._generate_final_report()
        
    async def _test_enhanced_links_system(self):
        """اختبار نظام الروابط المحسن"""
        test_name = "enhanced_links_system"
        logger.info("🔗 اختبار نظام الروابط المحسن...")
        
        try:
            # نص تجريبي يحتوي على أسماء شركات وألعاب
            test_content = """
            شركة Sony تعلن عن تحديث جديد للعبة God of War.
            Microsoft تطلق ميزات جديدة في Xbox Game Pass.
            لعبة Fortnite تحصل على تحديث جديد من Epic Games.
            Nintendo تكشف عن ألعاب جديدة لمنصة Switch.
            """
            
            # اختبار إضافة الروابط
            enhanced_content, links_data = internal_links_manager.add_internal_links_to_content(test_content)
            
            # فحص النتائج
            if links_data['total_links'] > 0:
                self._record_test_result(test_name, True, f"تم إضافة {links_data['total_links']} رابط")
                
                # فحص أن الروابط ليست فارغة
                if 'href=""' not in enhanced_content and 'href="#"' not in enhanced_content:
                    logger.info("✅ جميع الروابط تحتوي على URLs حقيقية")
                else:
                    self._record_test_result(test_name + "_no_empty_links", False, "توجد روابط فارغة")
                    
            else:
                self._record_test_result(test_name, False, "لم يتم إضافة أي روابط")
                
        except Exception as e:
            self._record_test_result(test_name, False, f"خطأ: {e}")

    async def _test_enhanced_image_search(self):
        """اختبار نظام البحث عن الصور المحسن"""
        test_name = "enhanced_image_search"
        logger.info("🖼️ اختبار نظام البحث عن الصور المحسن...")
        
        try:
            # اختبار البحث عن صور لعبة شهيرة
            test_game = "The Witcher 3"
            images = await licensed_image_manager.get_licensed_images_for_game(test_game, 2)
            
            if images:
                self._record_test_result(test_name, True, f"تم العثور على {len(images)} صورة لـ {test_game}")
                
                # فحص جودة النتائج
                for img in images:
                    if hasattr(img, 'source') and hasattr(img, 'url'):
                        logger.info(f"✅ صورة من {img.source}: {img.url[:50]}...")
                    else:
                        self._record_test_result(test_name + "_quality", False, "صورة بدون معلومات كاملة")
                        
            else:
                self._record_test_result(test_name, False, f"لم يتم العثور على صور لـ {test_game}")
                
        except Exception as e:
            self._record_test_result(test_name, False, f"خطأ: {e}")

    async def _test_general_article_images(self):
        """اختبار نظام إنشاء صور المقالات العامة"""
        test_name = "general_article_images"
        logger.info("🎨 اختبار نظام إنشاء صور المقالات العامة...")
        
        try:
            # مقال تجريبي عام
            test_article = {
                'title': 'أفضل 5 ألعاب مجانية على Steam في 2025',
                'content': 'قائمة بأفضل الألعاب المجانية المتاحة على منصة Steam، تشمل Dota 2 و Counter-Strike 2 و Team Fortress 2',
                'keywords': ['ألعاب مجانية', 'Steam', 'قائمة', 'أفضل ألعاب']
            }
            
            # اختبار إنشاء الصورة
            result = await general_article_image_generator.generate_image_for_general_article(test_article)
            
            if result and result.get('url'):
                self._record_test_result(test_name, True, f"تم إنشاء صورة: {result['filename']}")
                
                # فحص معلومات الصورة
                if result.get('generation_method') and result.get('creation_date'):
                    logger.info(f"✅ طريقة الإنشاء: {result['generation_method']}")
                else:
                    self._record_test_result(test_name + "_metadata", False, "معلومات الصورة ناقصة")
                    
            else:
                self._record_test_result(test_name, False, "فشل في إنشاء صورة للمقال العام")
                
        except Exception as e:
            self._record_test_result(test_name, False, f"خطأ: {e}")

    async def _test_smart_image_composition(self):
        """اختبار نظام التركيب الذكي للصور"""
        test_name = "smart_image_composition"
        logger.info("🎭 اختبار نظام التركيب الذكي للصور...")
        
        try:
            # مقال تجريبي للعبة محددة
            test_article = {
                'title': 'مراجعة شاملة للعبة Cyberpunk 2077 بعد التحديثات الجديدة',
                'content': 'تحليل مفصل للعبة Cyberpunk 2077 من CD Projekt Red بعد سلسلة التحديثات الأخيرة',
                'keywords': ['Cyberpunk 2077', 'مراجعة', 'CD Projekt Red']
            }
            
            # اختبار التركيب الذكي
            result = await smart_image_compositor.create_smart_composite(test_article, "Cyberpunk 2077")
            
            if result and result.get('url'):
                self._record_test_result(test_name, True, f"تم إنشاء تركيب ذكي: {result['filename']}")
                
                # فحص نوع التركيب
                if result.get('generation_method'):
                    logger.info(f"✅ نوع التركيب: {result['generation_method']}")
                    
            else:
                # اختبار التركيب العام كبديل
                general_result = await smart_image_compositor.create_smart_composite(test_article)
                if general_result:
                    self._record_test_result(test_name, True, "تم إنشاء تركيب عام كبديل")
                else:
                    self._record_test_result(test_name, False, "فشل في إنشاء أي نوع من التركيب")
                
        except Exception as e:
            self._record_test_result(test_name, False, f"خطأ: {e}")

    async def _test_full_integration(self):
        """اختبار التكامل الشامل"""
        test_name = "full_integration"
        logger.info("🔄 اختبار التكامل الشامل...")
        
        try:
            # مقال تجريبي شامل
            test_article = {
                'title': 'أحدث أخبار الألعاب: تحديثات Fortnite وإعلانات Sony الجديدة',
                'content': '''
                شركة Epic Games تعلن عن تحديث جديد للعبة Fortnite يتضمن خريطة جديدة.
                Sony تكشف عن ألعاب قادمة لمنصة PlayStation 5.
                Microsoft تطلق ميزات جديدة في Xbox Game Pass.
                ''',
                'keywords': ['Fortnite', 'Sony', 'PlayStation', 'أخبار ألعاب']
            }
            
            # اختبار النظام المتكامل
            # 1. تحديد نوع المقال
            article_type = smart_image_manager._determine_article_type(test_article)
            logger.info(f"🎯 نوع المقال المحدد: {article_type}")
            
            # 2. إنشاء صورة ذكية
            image_result = await smart_image_manager.generate_smart_image_for_article(test_article)
            
            # 3. إضافة روابط محسنة
            enhanced_content, links_data = internal_links_manager.add_internal_links_to_content(test_article['content'])
            
            # فحص النتائج
            success_count = 0
            
            if article_type:
                success_count += 1
                logger.info("✅ تحديد نوع المقال نجح")
                
            if image_result:
                success_count += 1
                logger.info("✅ إنشاء الصورة نجح")
                
            if links_data['total_links'] > 0:
                success_count += 1
                logger.info("✅ إضافة الروابط نجح")
            
            if success_count >= 2:
                self._record_test_result(test_name, True, f"نجح {success_count}/3 من المكونات")
            else:
                self._record_test_result(test_name, False, f"نجح {success_count}/3 فقط من المكونات")
                
        except Exception as e:
            self._record_test_result(test_name, False, f"خطأ: {e}")

    def _record_test_result(self, test_name: str, passed: bool, message: str):
        """تسجيل نتيجة الاختبار"""
        self.test_results['tests'][test_name] = {
            'passed': passed,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
        
        self.test_results['summary']['total_tests'] += 1
        if passed:
            self.test_results['summary']['passed'] += 1
            logger.info(f"✅ {test_name}: {message}")
        else:
            self.test_results['summary']['failed'] += 1
            logger.error(f"❌ {test_name}: {message}")

    def _generate_final_report(self):
        """إنشاء التقرير النهائي"""
        logger.info("📊 إنشاء التقرير النهائي...")
        
        # حفظ التقرير
        report_filename = f"enhanced_agent_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        # طباعة الملخص
        summary = self.test_results['summary']
        total = summary['total_tests']
        passed = summary['passed']
        failed = summary['failed']
        
        success_rate = (passed / total * 100) if total > 0 else 0
        
        logger.info("=" * 50)
        logger.info("📊 ملخص نتائج الاختبار:")
        logger.info(f"📈 إجمالي الاختبارات: {total}")
        logger.info(f"✅ نجح: {passed}")
        logger.info(f"❌ فشل: {failed}")
        logger.info(f"📊 معدل النجاح: {success_rate:.1f}%")
        logger.info(f"📄 التقرير المفصل: {report_filename}")
        logger.info("=" * 50)
        
        if success_rate >= 80:
            logger.info("🎉 التحسينات تعمل بشكل ممتاز!")
        elif success_rate >= 60:
            logger.info("⚠️ التحسينات تعمل بشكل جيد مع بعض المشاكل")
        else:
            logger.error("🚨 التحسينات تحتاج إلى مراجعة وإصلاح")


async def main():
    """الدالة الرئيسية"""
    tester = EnhancedAgentTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
