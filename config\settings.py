# إعدادات الوكيل البرمجي لأخبار الألعاب
import os
from typing import List, Dict, Any
from dotenv import load_dotenv
from modules.api_key_manager import ApiKeyManager

load_dotenv()

# --- Get the absolute path of the project root ---
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# --- مدير مفاتيح Google API المركزي ---
# يقرأ قائمة المفاتيح من متغير البيئة ويزيل أي مسافات بيضاء
google_api_keys_str = os.getenv("GOOGLE_API_KEYS_LIST", "")
google_api_keys = [key.strip() for key in google_api_keys_str.split(',') if key.strip()]

# إضافة المفتاح الرئيسي (GEMINI_API_KEY) إلى بداية القائمة إذا كان موجودًا
main_gemini_key = os.getenv("GEMINI_API_KEY")
if main_gemini_key and main_gemini_key not in google_api_keys:
    google_api_keys.insert(0, main_gemini_key)

# إضافة مفتاح YouTube الجديد في المقدمة (أولوية عالية)
youtube_api_key = "AIzaSyDopjKq-bRb2QuPICFlkR3WUsSb4_3vqPk"
if youtube_api_key not in google_api_keys:
    google_api_keys.insert(0, youtube_api_key)

# إضافة مفاتيح Google Search الجديدة إلى القائمة
new_google_search_keys = [
    # مفتاح YouTube API الجديد (مضاف من المستخدم)
    "AIzaSyDopjKq-bRb2QuPICFlkR3WUsSb4_3vqPk",

    # المجموعة الأولى
    "AIzaSyAEnnXE2U5ypT-XgxyZUMt8JiMFoa9bgU4",
    "AIzaSyBTjOvcIm_qDBx00U_3FqSlYEQI7kpOIlk",
    "AIzaSyDxvu1Z-DH7HnF_Z0y6OP13astfqeZMdkQ",
    "AIzaSyDfLuY9QV-Gg2LNTzWw7L8olw3sR3_sFDQ",
    "AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk",

    # المجموعة الثانية (الجديدة)
    "AIzaSyCpWpYWqYfG0XiaMvlJlnYGIeNvxVa-gfU",
    "AIzaSyBiq4VsF9RG0v5EDFiQ2YaDnsObx5JK3Ek",
    "AIzaSyDZ4WEJqhlGQZ0EFMt01awZB1rabEFphfU",
    "AIzaSyBYH-8POlNTPdsWwvHQfw6meyz6ikqEjRk",
    "AIzaSyALFblO8iqAWIMTVdiG0EHdy7mU8WkJk9o"
]

# إضافة المفاتيح الجديدة إلى القائمة إذا لم تكن موجودة
for key in new_google_search_keys:
    if key not in google_api_keys:
        google_api_keys.append(key)

# تحسينات مدير مفاتيح Gemini API
def create_enhanced_google_api_manager():
    """إنشاء مدير مفاتيح محسن مع معالجة أفضل للأخطاء"""
    try:
        # مفاتيح إضافية للاحتياط
        additional_keys = [
            "AIzaSyDjjgZIkGyC0i8RVM9IA14WFJBqmEQHhHE",  # المفتاح الجديد
            # يمكن إضافة المزيد هنا
        ]
        
        # دمج المفاتيح
        all_keys = google_api_keys.copy()
        for key in additional_keys:
            if key and key not in all_keys:
                all_keys.append(key)
        
        if all_keys:
            enhanced_manager = ApiKeyManager(
                api_keys=all_keys, 
                service_name="Google Enhanced",
                auto_recovery_minutes=30,  # استرداد أسرع
                load_balancing=True
            )
            return enhanced_manager
        else:
            return None
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء مدير المفاتيح المحسن: {e}")
        return None

# تهيئة مدير المفاتيح فقط إذا كانت هناك مفاتيح متاحة
if google_api_keys:
    # محاولة استخدام المدير المحسن أولاً
    google_api_manager_enhanced = create_enhanced_google_api_manager()
    if google_api_manager_enhanced:
        google_api_manager = google_api_manager_enhanced
    else:
        # العودة للمدير الأساسي
        google_api_manager = ApiKeyManager(api_keys=google_api_keys, service_name="Google")
else:
    google_api_manager = None

# سيتم تهيئة مدير مفاتيح Google Search بعد تعريف BotConfig
google_search_api_manager = None

# مدير Google Search المتقدم (سيتم تهيئته عند الحاجة)
google_search_manager_advanced = None

def get_google_search_manager():
    """الحصول على مدير Google Search المتقدم"""
    global google_search_manager_advanced

    if google_search_manager_advanced is None:
        try:
            from modules.google_search_manager import GoogleSearchManager

            # استخدام جميع المفاتيح المتاحة
            search_keys = [
                # المجموعة الأولى
                "AIzaSyAEnnXE2U5ypT-XgxyZUMt8JiMFoa9bgU4",
                "AIzaSyBTjOvcIm_qDBx00U_3FqSlYEQI7kpOIlk",
                "AIzaSyDxvu1Z-DH7HnF_Z0y6OP13astfqeZMdkQ",
                "AIzaSyDfLuY9QV-Gg2LNTzWw7L8olw3sR3_sFDQ",
                "AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk",

                # المجموعة الثانية (الجديدة)
                "AIzaSyCpWpYWqYfG0XiaMvlJlnYGIeNvxVa-gfU",
                "AIzaSyBiq4VsF9RG0v5EDFiQ2YaDnsObx5JK3Ek",
                "AIzaSyDZ4WEJqhlGQZ0EFMt01awZB1rabEFphfU",
                "AIzaSyBYH-8POlNTPdsWwvHQfw6meyz6ikqEjRk",
                "AIzaSyALFblO8iqAWIMTVdiG0EHdy7mU8WkJk9o"
            ]

            search_engine_id = os.getenv("GOOGLE_SEARCH_ENGINE_ID", "")

            if search_keys and search_engine_id:
                google_search_manager_advanced = GoogleSearchManager(
                    api_keys=search_keys,
                    search_engine_id=search_engine_id
                )
                print(f"🔍 تم تهيئة مدير Google Search المتقدم بنجاح")
            else:
                print("⚠️ لا يمكن تهيئة مدير Google Search المتقدم - مفاتيح أو معرف محرك البحث مفقود")

        except Exception as e:
            print(f"❌ خطأ في تهيئة مدير Google Search المتقدم: {e}")

    return google_search_manager_advanced


# إعدادات PageSpeed Insights
PAGESPEED_INSIGHTS_API_KEY = "AIzaSyDjjgZIkGyC0i8RVM9IA14WFJBqmEQHhHE"  # يمكن استخدام نفس مفتاح Gemini

# إعدادات SEO محسنة
class EnhancedSEOConfig:
    """إعدادات SEO محسنة"""
    
    # حدود العنوان المحسنة
    TITLE_LENGTH_MIN = 30
    TITLE_LENGTH_MAX = 60
    
    # حدود الوصف
    META_DESCRIPTION_MIN = 120
    META_DESCRIPTION_MAX = 160
    
    # حدود المحتوى
    CONTENT_MIN_WORDS = 300
    CONTENT_OPTIMAL_WORDS = 800
    
    # كثافة الكلمات المفتاحية
    KEYWORD_DENSITY_MIN = 0.5  # 0.5%
    KEYWORD_DENSITY_MAX = 3.0  # 3%
    
    # نقاط SEO المستهدفة
    TARGET_SEO_SCORE = 80
    MINIMUM_SEO_SCORE = 60
    
    # Core Web Vitals المستهدفة
    TARGET_LCP = 2.5  # Largest Contentful Paint (ثواني)
    TARGET_FID = 100  # First Input Delay (ميلي ثانية)
    TARGET_CLS = 0.1  # Cumulative Layout Shift
    
    # أوزان حساب النقاط
    SCORE_WEIGHTS = {
        'title_optimization': 0.20,
        'content_quality': 0.25,
        'keyword_optimization': 0.20,
        'technical_seo': 0.15,
        'user_experience': 0.10,
        'mobile_optimization': 0.10
    }

# تطبيق الإعدادات المحسنة
SEOConfig = EnhancedSEOConfig()

class BotConfig:
    """تكوين البوت الأساسي"""
    
    # تم إزالة معلومات بوت تيليجرام - الآن يعتمد على الواجهة الويب فقط
    # TELEGRAM_BOT_TOKEN = ""  # تم إزالة Telegram
    # TELEGRAM_BOT_USERNAME = ""  # تم إزالة Telegram
    # TELEGRAM_CHANNEL_URL = ""  # تم إزالة Telegram
    # TELEGRAM_CHANNEL_ID = ""  # تم إزالة Telegram
    # TELEGRAM_ADMIN_ID = ""  # تم إزالة Telegram - الموافقة الآن عبر الواجهة الويب
    
    # مفاتيح API (يتم إدارتها الآن عبر google_api_manager)
    GEMINI_API_KEY = main_gemini_key # احتفاظ بالمرجع الرئيسي
    BLOGGER_CLIENT_ID = os.getenv("BLOGGER_CLIENT_ID", "")
    BLOGGER_CLIENT_SECRET = os.getenv("BLOGGER_CLIENT_SECRET", "")
    BLOGGER_BLOG_ID = os.getenv("BLOGGER_BLOG_ID", "")
    BLOGGER_CLIENT_SECRET_FILE = os.path.join(PROJECT_ROOT, os.getenv("BLOGGER_CLIENT_SECRET_FILE", "client_secret.json"))
    # إعدادات Txtify المحسنة مع روابط احتياطية
    TXTIFY_API_URLS = ['https://nanami34-ai55.hf.space', 'https://txtify-api.hf.space', 'http://localhost:8000']
    TXTIFY_API_URL = TXTIFY_API_URLS[0]  # الرابط الأساسي
    TXTIFY_FALLBACK_ENABLED = True  # تفعيل النظام الاحتياطي
    TXTIFY_TIMEOUT_SECONDS = 300  # مهلة 5 دقائق
    GOOGLE_SEARCH_ENGINE_ID = os.getenv("GOOGLE_SEARCH_ENGINE_ID", "")

    # إعدادات الموقع والعلامة المائية
    WEBSITE_NAME = os.getenv("WEBSITE_NAME", "Gaming News")  # اسم الموقع للعلامة المائية

    # مفاتيح APIs للأخبار المتقدمة - نظام جديد قوي!
    NEWSAPI_KEY = os.getenv("NEWSAPI_KEY", "")  # NewsAPI.org
    NEWSDATA_KEY = os.getenv("NEWSDATA_KEY", "pub_6a04788f4edc429a8fb798dc3af6a6fb")  # NewsData.io
    THENEWSAPI_KEY = os.getenv("THENEWSAPI_KEY", "")  # TheNewsAPI.com
    GNEWS_KEY = os.getenv("GNEWS_KEY", "")  # GNews.io

    # مفاتيح APIs للبحث المتقدم
    BRAVE_SEARCH_KEY = os.getenv("BRAVE_SEARCH_KEY", "")  # Brave Search API

    # مفاتيح Search1API - بديل ممتاز لـ Tavily
    SEARCH1API_KEYS = [
        os.getenv("SEARCH1API_KEY_1", ""),  # المفتاح الأول
        os.getenv("SEARCH1API_KEY_2", ""),  # المفتاح الثاني
        os.getenv("SEARCH1API_KEY_3", ""),  # المفتاح الثالث
    ]
    SEARCH1API_KEYS = [key for key in SEARCH1API_KEYS if key]  # إزالة المفاتيح الفارغة

    # مفاتيح APIs البديلة لـ Google Search
    SERPAPI_KEY = os.getenv("SERPAPI_KEY", "8b221d23f3aa037d438db307927f904933ae3037")  # SerpAPI Key الجديد الأول
    RAPIDAPI_KEY = os.getenv("RAPIDAPI_KEY", "**************************************************")  # RapidAPI Key (احتياطي)
    SERPAPI_RAPIDAPI_HOST = "serpapi.p.rapidapi.com"  # SerpAPI عبر RapidAPI
    BING_SEARCH_KEY = os.getenv("BING_SEARCH_KEY", "")  # Bing Web Search API
    ZENSERP_KEY = os.getenv("ZENSERP_KEY", "")  # Zenserp API

    # مفاتيح APIs للصور المرخصة - نظام جديد آمن قانونياً!
    TWITCH_CLIENT_ID = os.getenv("TWITCH_CLIENT_ID", "")  # IGDB API (Twitch)
    TWITCH_CLIENT_SECRET = os.getenv("TWITCH_CLIENT_SECRET", "")  # IGDB API Secret
    RAWG_API_KEY = os.getenv("RAWG_API_KEY", "")  # RAWG.io API
    STEAM_API_KEY = os.getenv("STEAM_API_KEY", "")  # Steam Web API (اختياري)



    # مفاتيح Google Search متعددة للتوزيع والاحتياط
    GOOGLE_SEARCH_KEYS = [
        # المجموعة الأولى (المفاتيح السابقة)
        "AIzaSyAEnnXE2U5ypT-XgxyZUMt8JiMFoa9bgU4",
        "AIzaSyBTjOvcIm_qDBx00U_3FqSlYEQI7kpOIlk",
        "AIzaSyDxvu1Z-DH7HnF_Z0y6OP13astfqeZMdkQ",
        "AIzaSyDfLuY9QV-Gg2LNTzWw7L8olw3sR3_sFDQ",
        "AIzaSyC3ihvVEvFSgaw_MAjcJLBZQno_8L1F-Tk",

        # المجموعة الثانية (المفاتيح الجديدة)
        "AIzaSyCpWpYWqYfG0XiaMvlJlnYGIeNvxVa-gfU",
        "AIzaSyBiq4VsF9RG0v5EDFiQ2YaDnsObx5JK3Ek",
        "AIzaSyDZ4WEJqhlGQZ0EFMt01awZB1rabEFphfU",
        "AIzaSyBYH-8POlNTPdsWwvHQfw6meyz6ikqEjRk",
        "AIzaSyALFblO8iqAWIMTVdiG0EHdy7mU8WkJk9o"
    ]

    # المفتاح الافتراضي (للتوافق مع الكود الموجود)
    GOOGLE_SEARCH_KEY = os.getenv("GOOGLE_SEARCH_KEY", GOOGLE_SEARCH_KEYS[0])  # Google Custom Search

    # مفاتيح APIs للبحث العميق باستخدام MCP وخدمات AI مجانية
    PERPLEXITY_API_KEY = os.getenv("PERPLEXITY_API_KEY", "")  # Perplexity AI (5 استعلامات/يوم مجاناً)
    YOU_COM_API_KEY = os.getenv("YOU_COM_API_KEY", "")  # You.com (100 استعلام/يوم مجاناً)
    SERPER_API_KEY = os.getenv("SERPER_API_KEY", "")  # Serper (2500 استعلام/شهر مجاناً)

    # مفاتيح Tavily API للبحث العميق مع الذكاء الاصطناعي (الأولوية الأولى)
    TAVILY_API_KEYS = [
        os.getenv("TAVILY_API_KEY_1", "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"),  # المفتاح الأول
        os.getenv("TAVILY_API_KEY_2", "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"),  # المفتاح الثاني
    ]

    # ========================================
    # 🔍 البدائل المحسنة لـ Tavily - نظام جديد قوي!
    # ========================================

    # Search1API - بديل ممتاز لـ Tavily (الأولوية الثانية)
    # ✅ 20-200 طلب/يوم مجاناً
    # ✅ جودة نتائج عالية
    # ✅ لا يتطلب بطاقة ائتمان
    # 🌐 احصل على مفاتيح من: https://search1api.com

    # مفاتيح Search1API - بديل ممتاز لـ Tavily (الأولوية الثانية)
    SEARCH1API_KEYS = [
        os.getenv("SEARCH1API_KEY_1", ""),  # المفتاح الأول - خطة Keyless (20 طلب/يوم)
        os.getenv("SEARCH1API_KEY_2", ""),  # المفتاح الثاني - خطة Keyless (20 طلب/يوم)
        os.getenv("SEARCH1API_KEY_3", ""),  # المفتاح الثالث - خطة Developer (200 طلب/يوم)
    ]
    SEARCH1API_KEYS = [key for key in SEARCH1API_KEYS if key]  # إزالة المفاتيح الفارغة

    # Brave Search API - بحث مجاني قوي (الأولوية الثالثة)
    # ✅ 5,000 استفسار/شهر مجاناً
    # ✅ بحث مستقل وخصوصية عالية
    # ✅ لا يتطلب بطاقة ائتمان
    # 🌐 احصل على مفتاح من: https://brave.com/search/api/
    BRAVE_SEARCH_KEY = os.getenv("BRAVE_SEARCH_KEY", "")  # مفتاح Brave Search API

    TAVILY_API_KEY = TAVILY_API_KEYS[0]  # المفتاح الافتراضي للتوافق مع الكود الموجود

    # مفاتيح APIs البديلة الجديدة للبحث المتقدم (مجانية 100%)
    # 1. SerpAPI - مفاتيح متعددة للتوزيع
    SERPAPI_KEYS = [
        os.getenv("SERPAPI_KEY_1", ""),  # المفتاح الأول
        os.getenv("SERPAPI_KEY_2", ""),  # المفتاح الثاني
        os.getenv("SERPAPI_KEY_3", ""),  # المفتاح الثالث
    ]

    # 2. ScraperAPI - للاستخراج المتقدم
    SCRAPERAPI_KEYS = [
        os.getenv("SCRAPERAPI_KEY_1", ""),  # المفتاح الأول
        os.getenv("SCRAPERAPI_KEY_2", ""),  # المفتاح الثاني
    ]

    # 3. Zyte (Scrapinghub) - للاستخراج الموزع
    ZYTE_API_KEYS = [
        os.getenv("ZYTE_API_KEY_1", ""),  # المفتاح الأول
        os.getenv("ZYTE_API_KEY_2", ""),  # المفتاح الثاني
    ]

    # 4. ContextualWeb Search API - للبحث العام
    CONTEXTUALWEB_KEYS = [
        os.getenv("CONTEXTUALWEB_KEY_1", ""),  # المفتاح الأول
        os.getenv("CONTEXTUALWEB_KEY_2", ""),  # المفتاح الثاني
    ]

    # 5. Serper.dev - للذكاء الاصطناعي
    SERPER_DEV_KEYS = [
        os.getenv("SERPER_DEV_KEY_1", ""),  # المفتاح الأول
        os.getenv("SERPER_DEV_KEY_2", ""),  # المفتاح الثاني
    ]

    # 6. Google Custom Search JSON API - احتياطي
    GOOGLE_CUSTOM_SEARCH_KEYS = [
        os.getenv("GOOGLE_CUSTOM_SEARCH_KEY_1", ""),  # المفتاح الأول
        os.getenv("GOOGLE_CUSTOM_SEARCH_KEY_2", ""),  # المفتاح الثاني
    ]
    GOOGLE_CUSTOM_SEARCH_ENGINE_ID = os.getenv("GOOGLE_CUSTOM_SEARCH_ENGINE_ID", "")

    # إعدادات نظام YouTube المتقدم مع Whisper
    YOUTUBE_API_KEY = os.getenv("YOUTUBE_API_KEY", GOOGLE_SEARCH_KEYS[0] if GOOGLE_SEARCH_KEYS else "")  # مفتاح YouTube Data API
    WHISPER_API_URL = os.getenv("WHISPER_API_URL", "https://nanami34-ai55.hf.space/api/transcribe")
    WHISPER_API_KEY = os.getenv("WHISPER_API_KEY", "whisper-hf-spaces-2025")
    HF_TOKEN = os.getenv("HF_TOKEN", "*************************************")

    # إعدادات خدمات تحويل النص إلى صوت المتعددة - نظام جديد محسن!
    # 🎯 الخدمات الأساسية (مجانية مع حدود سخية)

    # 1. AssemblyAI - 416 ساعة مجانية للاختبار
    ASSEMBLYAI_API_KEY = os.getenv("ASSEMBLYAI_API_KEY", "")
    ASSEMBLYAI_API_URL = "https://api.assemblyai.com/v2/transcript"
    ASSEMBLYAI_UPLOAD_URL = "https://api.assemblyai.com/v2/upload"
    ASSEMBLYAI_FREE_HOURS = 416  # ساعات مجانية

    # 2. Speechmatics - 8 ساعات (480 دقيقة) مجانية شهرياً
    SPEECHMATICS_API_KEY = os.getenv("SPEECHMATICS_API_KEY", "")
    SPEECHMATICS_API_URL = "https://asr.api.speechmatics.com/v2/jobs"
    SPEECHMATICS_FREE_MINUTES_MONTHLY = 480  # دقائق مجانية شهرياً

    # 3. IBM Watson Speech-to-Text - 500 دقيقة مجانية شهرياً
    IBM_WATSON_API_KEY = os.getenv("IBM_WATSON_API_KEY", "")
    IBM_WATSON_URL = os.getenv("IBM_WATSON_URL", "")
    IBM_WATSON_FREE_MINUTES_MONTHLY = 500  # دقائق مجانية شهرياً

    # 4. Microsoft Azure Speech-to-Text - 300 دقيقة (5 ساعات) مجانية شهرياً
    AZURE_SPEECH_KEY = os.getenv("AZURE_SPEECH_KEY", "")
    AZURE_SPEECH_REGION = os.getenv("AZURE_SPEECH_REGION", "")
    AZURE_SPEECH_ENDPOINT = f"https://{AZURE_SPEECH_REGION}.stt.speech.microsoft.com/speech/recognition/conversation/cognitiveservices/v1"
    AZURE_FREE_MINUTES_MONTHLY = 300  # دقائق مجانية شهرياً

    # 5. Google Cloud Speech-to-Text - 60 دقيقة مجانية شهرياً
    GOOGLE_CLOUD_SPEECH_KEY = os.getenv("GOOGLE_CLOUD_SPEECH_KEY", "")
    GOOGLE_CLOUD_SPEECH_PROJECT_ID = os.getenv("GOOGLE_CLOUD_SPEECH_PROJECT_ID", "")
    GOOGLE_CLOUD_FREE_MINUTES_MONTHLY = 60  # دقائق مجانية شهرياً

    # 6. Wit.ai - مجاني تماماً (بما في ذلك الاستخدام التجاري)
    WITAI_ACCESS_TOKEN = os.getenv("WITAI_ACCESS_TOKEN", "")
    WITAI_API_URL = "https://api.wit.ai/speech"
    WITAI_FREE_UNLIMITED = True  # مجاني بلا حدود

    # ========================================
    # إعدادات تحميل الصوت المحسن - نظام جديد محسن!
    # ========================================

    # 🔵 1. Apify YouTube to MP3 API - الأولوية الأولى
    APIFY_API_TOKEN = os.getenv("APIFY_API_TOKEN", "")
    APIFY_ACTOR_ID = "drobnikj/youtube-downloader"
    APIFY_API_URL = "https://api.apify.com/v2"
    APIFY_FREE_CREDIT_MONTHLY = 5  # $5 شهرياً = حوالي 12,500 وحدة
    APIFY_COST_PER_RUN = 0.0004  # تكلفة تقريبية لكل تشغيل

    # 🟠 2. youtube-dl - الأولوية الثانية (مجاني بالكامل)
    YOUTUBE_DL_ENABLED = True
    YOUTUBE_DL_PATH = os.getenv("YOUTUBE_DL_PATH", "youtube-dl")  # مسار الأداة
    FFMPEG_PATH = os.getenv("FFMPEG_PATH", "ffmpeg")  # مسار ffmpeg
    YOUTUBE_DL_OUTPUT_DIR = "temp/youtube_dl"  # مجلد التحميل المؤقت

    # إعدادات جودة الصوت
    AUDIO_QUALITY_SETTINGS = {
        "high": {
            "format": "bestaudio/best",
            "audio_format": "mp3",
            "audio_quality": "192k"
        },
        "medium": {
            "format": "bestaudio[abr<=128]/best[abr<=128]",
            "audio_format": "mp3",
            "audio_quality": "128k"
        },
        "low": {
            "format": "bestaudio[abr<=64]/best[abr<=64]",
            "audio_format": "mp3",
            "audio_quality": "64k"
        }
    }

    # إعدادات أولوية طرق التحميل
    AUDIO_DOWNLOAD_PRIORITIES = {
        "apify": 1,           # الأولوية الأولى - جودة عالية وسرعة
        "youtube_dl": 2,      # الأولوية الثانية - مجاني بالكامل
        "yt_dlp": 3,          # البديل الثالث (إذا كان متوفر)
        "pytube": 4,          # البديل الرابع (الطريقة الحالية)
        "youtube_api": 5      # البديل الأخير (الطريقة الحالية)
    }

    # مفاتيح النماذج الاحتياطية للبحث العميق - تم التحديث لاستخدام Gemini 2.5 Pro فقط
    # GEMINI_2_FLASH_API_KEY = os.getenv("GEMINI_2_FLASH_API_KEY", "")  # تم إلغاؤه - نستخدم 2.5 Pro
    DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "")  # DeepSeek R1 - تفكير عميق + بحث
    GROQ_API_KEY = os.getenv("GROQ_API_KEY", "")  # Groq API - سرعة عالية
    # GEMINI_1_5_FLASH_API_KEY = os.getenv("GEMINI_1_5_FLASH_API_KEY", "")  # تم إلغاؤه - نستخدم 2.5 Pro

    # إعدادات فلترة الفيديوهات
    MAX_VIDEO_DURATION_MINUTES = int(os.getenv("MAX_VIDEO_DURATION_MINUTES", "30"))  # 30 دقيقة كحد أقصى
    MAX_VIDEO_AGE_DAYS = int(os.getenv("MAX_VIDEO_AGE_DAYS", "60"))  # شهرين كحد أقصى

    # إعدادات نظام الموافقة
    APPROVAL_TIMEOUT_MINUTES = int(os.getenv("APPROVAL_TIMEOUT_MINUTES", "5"))  # 5 دقائق للموافقة
    AUTO_APPROVE_ON_TIMEOUT = os.getenv("AUTO_APPROVE_ON_TIMEOUT", "true").lower() == "true"

    # مفاتيح APIs للصور الآمنة - ImageGuard Pro
    PEXELS_API_KEY = os.getenv("PEXELS_API_KEY", "")  # مفتاح Pexels API (مجاني - 200 طلب/ساعة)
    PIXABAY_API_KEY = os.getenv("PIXABAY_API_KEY", "")  # مفتاح Pixabay API (مجاني - 5000 طلب/شهر)
    UNSPLASH_ACCESS_KEY = os.getenv("UNSPLASH_ACCESS_KEY", "")  # مفتاح Unsplash API (مجاني - 50 طلب/ساعة)

    # مفاتيح APIs لإنشاء الصور بالذكاء الاصطناعي - AI Image Generation
    # 🎯 Pollinations.AI - الطريقة الأساسية الجديدة (مجاني 100% - لا يحتاج مفتاح!)
    POLLINATIONS_AI_URL = "https://image.pollinations.ai/prompt/"  # URL أساسي لـ Pollinations.AI
    POLLINATIONS_AI_ENABLED = True  # تفعيل Pollinations.AI كطريقة أساسية

    # APIs احتياطية (تستخدم عند فشل Pollinations.AI)
    FREEPIK_API_KEY = os.getenv("FREEPIK_API_KEY", "FPSX1ee910637a8ec349e6d8c7f17a57740b")  # مفتاح Freepik API المحدث
    FLUXAI_API_KEY = os.getenv("FLUXAI_API_KEY", "b6863038ac459a1f8cd9e30d82cdd989")  # مفتاح FluxAI API
    LEONARDO_AI_API_KEY = os.getenv("LEONARDO_AI_API_KEY", "")  # مفتاح Leonardo AI (اختياري)
    MIDJOURNEY_API_KEY = os.getenv("MIDJOURNEY_API_KEY", "")  # مفتاح Midjourney (اختياري)

    # إعدادات التشغيل
    SEARCH_INTERVAL_HOURS = 2  # البحث كل ساعتين
    MAX_RETRIES = 3  # عدد محاولات إعادة التنفيذ
    RETRY_DELAY = 30  # تأخير بين المحاولات (ثانية)
    
    # إعدادات قاعدة البيانات
    DATABASE_PATH = "data/articles.db"
    
    # إعدادات التسجيل
    LOG_LEVEL = "INFO"
    LOG_FILE = "logs/bot.log"
    
    # حدود API
    GEMINI_RATE_LIMIT = 60  # طلبات في الدقيقة
    # TELEGRAM_RATE_LIMIT = 30  # تم إزالة Telegram
    BLOGGER_RATE_LIMIT = 100  # طلبات في اليوم

    # حدود APIs الصور
    PEXELS_RATE_LIMIT = 200  # طلبات في الساعة
    PIXABAY_RATE_LIMIT = 5000  # طلبات في الشهر
    UNSPLASH_RATE_LIMIT = 50  # طلبات في الساعة

    # حدود APIs إنشاء الصور بالذكاء الاصطناعي
    POLLINATIONS_AI_RATE_LIMIT = 999999  # لا توجد حدود (مجاني بالكامل)
    FREEPIK_RATE_LIMIT = 100  # طلبات في اليوم (حسب الخطة)
    FLUXAI_RATE_LIMIT = 1000  # طلبات في اليوم (مجاني)
    LEONARDO_AI_RATE_LIMIT = 150  # طلبات في اليوم
    MIDJOURNEY_RATE_LIMIT = 25  # طلبات في الساعة

    # حدود APIs الأخبار المتقدمة
    NEWSAPI_RATE_LIMIT = 1000  # طلبات في اليوم (خطة مجانية)
    NEWSDATA_RATE_LIMIT = 200  # طلبات في اليوم (خطة مجانية)
    THENEWSAPI_RATE_LIMIT = 100  # طلبات في اليوم
    GNEWS_RATE_LIMIT = 100  # طلبات في اليوم

    # حدود APIs البحث المتقدم
    BRAVE_SEARCH_RATE_LIMIT = 2000  # طلبات في الشهر (خطة مجانية)
    GOOGLE_SEARCH_RATE_LIMIT = 100  # طلبات في اليوم (خطة مجانية)
    
    @classmethod
    def validate_config(cls) -> bool:
        """التحقق من صحة التكوين"""
        required_fields = [
            "GEMINI_API_KEY",
            "BLOGGER_CLIENT_ID",
            "BLOGGER_CLIENT_SECRET",
            "BLOGGER_BLOG_ID"
        ]

        for field in required_fields:
            if not getattr(cls, field):
                print(f"⚠️ حقل مطلوب غير موجود: {field}")
                return False
        return True

# تم إزالة Google Search API لتجنب الحظر - استخدام النظام الذكي البديل
google_search_api_manager = None
print("🔍 تم تعطيل Google Search API - سيتم استخدام النظام الذكي البديل")

class SourcesConfig:
    """تكوين مصادر البيانات"""

    # المواقع الرسمية
    OFFICIAL_SOURCES = [
        "https://www.gamespot.com",
        "https://www.polygon.com",
        "https://www.kotaku.com",
        "https://www.eurogamer.net",
        "https://www.gamesindustry.biz",
        "https://www.gamedeveloper.com"
    ]

    # مواقع الألعاب المتخصصة
    GAMING_SITES = [
        "https://ign.com",
        "https://commonsensemedia.org",
        "https://pcgamesn.com",
        "https://www.gamesradar.com",
        "https://www.destructoid.com",
        "https://www.rockpapershotgun.com",
        "https://www.pcgamer.com",
        "https://www.gameinformer.com",
        "https://www.theverge.com/games",
        "https://arstechnica.com/gaming"
    ]

    # المواقع العربية
    ARABIC_SITES = [
        "https://vga4a.com",
        "https://www.true-gaming.net",
        "https://saudigamer.com",
        # "https://www.arageek.com/tech/gaming",  # معطل - 404
        # "https://www.tech-wd.com/wd/category/games",  # معطل - 404
        "https://www.i3lam.com/category/games"
    ]

    # مواقع المراجعات
    REVIEW_SITES = [
        "https://www.ign.com/reviews/games",
        "https://www.gamespot.com/reviews/",
        "https://www.metacritic.com/game",
        "https://opencritic.com",
        "https://www.giantbomb.com/reviews"
    ]

    # المنتديات
    FORUM_SITES = [
        # "https://www.resetera.com/forums/gaming-forum.4/",  # معطل - 404
        "https://www.reddit.com/r/gamingnews/",
        # "https://www.neogaf.com/forums/gaming.2/",  # معطل - 403 Forbidden
        "https://www.reddit.com/r/Games/",
        "https://www.reddit.com/r/gaming/"
    ]

    # مصادر الأخبار السريعة
    NEWS_AGGREGATORS = [
        "https://news.google.com/topics/CAAqJggKIiBDQkFTRWdvSUwyMHZNREZqY0hsNUVnVnVaWGR6S0FBUAE",
        "https://www.gamedev.net/news",
        "https://www.gamasutra.com/news"
    ]

    # مصادر التحديثات والإعلانات
    ANNOUNCEMENT_SOURCES = [
        "https://blog.playstation.com",
        "https://news.xbox.com",
        "https://www.nintendo.com/us/whatsnew",
        "https://store.steampowered.com/news",
        "https://blog.epicgames.com"
    ]
    
    # قنوات يوتيوب واستعلامات البحث المحسنة
    YOUTUBE_CHANNELS = [
        "new game trailers 2025",
        "upcoming games 2025",
        "video game news today",
        "أخبار ألعاب الفيديو",
        "game reviews 2025",
        "مراجعات ألعاب جديدة",
        "gameplay footage new",
        "new game releases this week",
        "gaming announcements",
        "indie games 2025",
        "AAA games news",
        "mobile games updates",
        "PC gaming news",
        "console gaming updates",
        "VR games 2025",
        "esports news",
        "gaming industry news",
        "game development updates",
        "sandbox games updates",
        "battle royale news",
        "fps games updates",
        "sports games 2025",
        "open world games news",
        "rpg games updates",
        "indie games news",
        "mobile gaming updates"
    ]
    
    # حسابات تويتر
    TWITTER_ACCOUNTS = [
        "@IGN",
        "@GameSpot",
        "@PlayStation",
        "@Xbox"
    ]
    
    # متاجر التطبيقات
    APP_STORES = [
        "https://play.google.com",
        "https://store.steampowered.com"
    ]

class ContentConfig:
    """تكوين المحتوى"""
    
    # أنواع المحتوى
    CONTENT_TYPES = [
        "أخبار_الألعاب",
        "تحديثات_الألعاب",
        "مراجعات_جديدة",
        "عروض_خاصة",
        "مقالات_رأي"
    ]
    
    # اللهجات المدعومة
    DIALECTS = {
        "standard": "العربية الفصحى",
        "egyptian": "مصرية",
        "saudi": "سعودية"
    }
    DEFAULT_DIALECT = "standard"  # اللهجة الافتراضية
    
    # تنسيقات النشر على تيليجرام
    TELEGRAM_FORMATS = [
        "ملخص_قصير",
        "نقاط_رئيسية", 
        "سؤال_وجواب",
        "اقتباس",
        "صورة_مع_نص"
    ]
    
    # كلمات مفتاحية أساسية محسنة
    BASE_KEYWORDS = [
        "ألعاب الفيديو",
        "video games",
        "أخبار الألعاب",
        "gaming news",
        "تحديثات الألعاب",
        "game updates",
        "إصدارات جديدة",
        "new releases",
        "مراجعات الألعاب",
        "game reviews",
        "ألعاب 2025",
        "games 2025",
        "ألعاب الكمبيوتر",
        "PC games",
        "ألعاب الموبايل",
        "mobile games",
        "ألعاب الكونسول",
        "console games",
        "ألعاب مجانية",
        "free games",
        "ألعاب مدفوعة",
        "paid games",
        "ألعاب أونلاين",
        "online games",
        "ألعاب أوفلاين",
        "offline games"
    ]
    
    # فئات المقالات
    ARTICLE_CATEGORIES = [
        "أخبار الألعاب",
        "تحديثات الألعاب",
        "مراجعات وتحليلات",
        "مقالات رأي",
        "أخبار الشركات"
    ]

class SEOConfig:
    """تكوين تحسين محركات البحث المحسن"""

    # طول العنوان المثالي - تم زيادة الحد الأقصى لحل مشكلة قطع العناوين
    TITLE_LENGTH_MIN = 30
    TITLE_LENGTH_MAX = 120  # زيادة من 60 إلى 120 حرف للعناوين العربية

    # طول الوصف التعريفي
    META_DESCRIPTION_LENGTH = 155

    # كثافة الكلمات المفتاحية
    KEYWORD_DENSITY = 2.0  # نسبة مئوية محسنة

    # عدد الكلمات المفتاحية لكل مقال
    MAX_KEYWORDS_PER_ARTICLE = 15

    # قوالب العناوين الجذابة والمحسنة لـ SEO
    TITLE_TEMPLATES = [
        "🔥 خبر عاجل: {content} - أحدث أخبار الألعاب",
        "⚡ دليل شامل: {content} - كل ما تحتاج معرفته",
        "🎮 مراجعة حصرية: {content} - تقييم مفصل",
        "🚀 تحديث جديد: {content} - آخر التطورات",
        "💎 اكتشف: {content} - أفضل الألعاب الجديدة",
        "🏆 أفضل: {content} - قائمة محدثة 2025",
        "📱 أخبار: {content} - تحديثات يومية",
        "🎯 تحليل: {content} - رؤية عميقة",
        "⭐ مميز: {content} - محتوى حصري",
        "🔍 استكشف: {content} - دليل المبتدئين"
    ]

    # كلمات مفتاحية عالية الأداء
    HIGH_PERFORMANCE_KEYWORDS = [
        "أفضل الألعاب",
        "best games",
        "مراجعة لعبة",
        "game review",
        "تحديث جديد",
        "new update",
        "ألعاب مجانية",
        "free games",
        "نصائح وحيل",
        "tips and tricks",
        "دليل اللعبة",
        "game guide"
    ]

    # عبارات دعوة للعمل
    CALL_TO_ACTION_PHRASES = [
        "شاركنا رأيك في التعليقات",
        "ما رأيكم في هذا التحديث؟",
        "أخبرونا عن تجربتكم مع اللعبة",
        "هل جربتم هذه اللعبة من قبل؟",
        "ما هي لعبتكم المفضلة؟",
        "انتظروا المزيد من المراجعات",
        "تابعونا للحصول على آخر الأخبار",
        "لا تفوتوا أحدث التحديثات"
    ]

class ImageSafetyConfig:
    """تكوين أمان الصور - ImageGuard Pro"""

    # كلمات آمنة للبحث عن الصور
    SAFE_KEYWORDS = [
        'gaming', 'controller', 'console', 'computer', 'technology',
        'esports', 'digital', 'modern', 'setup', 'workspace', 'keyboard',
        'mouse', 'headset', 'monitor', 'screen', 'device', 'electronic'
    ]

    # كلمات محظورة لضمان الامتثال لـ AdSense
    FORBIDDEN_KEYWORDS = [
        'violence', 'blood', 'weapon', 'gun', 'fight', 'war', 'battle',
        'alcohol', 'beer', 'wine', 'cigarette', 'smoking', 'tobacco',
        'gambling', 'casino', 'poker', 'bet', 'adult', 'sexy', 'nude'
    ]

    # أولوية مصادر الصور (من الأفضل للأقل)
    SOURCE_PRIORITY = ['Pexels', 'Pixabay', 'Unsplash']

    # الحد الأدنى لجودة الصور
    MIN_IMAGE_WIDTH = 400
    MIN_IMAGE_HEIGHT = 300

    # الرخص المسموحة
    ALLOWED_LICENSES = [
        'Pexels License',
        'Pixabay License',
        'Unsplash License',
        'Creative Commons CC0'
    ]

    # صور احتياطية آمنة (URLs ثابتة)
    FALLBACK_IMAGES = [
        {
            'url': 'https://images.pexels.com/photos/442576/pexels-photo-442576.jpeg',
            'description': 'Gaming controller on dark background',
            'license': 'Pexels License',
            'attribution': 'Photo by Lucie Liz from Pexels'
        },
        {
            'url': 'https://images.pexels.com/photos/1174746/pexels-photo-1174746.jpeg',
            'description': 'Modern gaming setup with RGB lighting',
            'license': 'Pexels License',
            'attribution': 'Photo by FOX from Pexels'
        },
        {
            'url': 'https://images.pexels.com/photos/735911/pexels-photo-735911.jpeg',
            'description': 'Retro gaming console and controller',
            'license': 'Pexels License',
            'attribution': 'Photo by Garrett Morrow from Pexels'
        },
        {
            'url': 'https://images.pexels.com/photos/194511/pexels-photo-194511.jpeg',
            'description': 'Gaming keyboard with colorful backlighting',
            'license': 'Pexels License',
            'attribution': 'Photo by Lukas from Pexels'
        }
    ]
