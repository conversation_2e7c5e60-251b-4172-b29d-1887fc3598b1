#!/usr/bin/env python3
# اختبار الأنظمة الجديدة

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_intelligent_search():
    """اختبار نظام البحث الذكي البديل"""
    print("🔍 اختبار نظام البحث الذكي البديل...")
    
    try:
        from modules.intelligent_search_system import intelligent_search
        
        # اختبار البحث
        results = await intelligent_search.search(
            query="gaming news today",
            max_results=5,
            search_type='gaming_news'
        )
        
        print(f"✅ تم العثور على {len(results)} نتيجة")
        
        for i, result in enumerate(results[:3], 1):
            print(f"  {i}. {result.get('title', 'بدون عنوان')}")
            print(f"     {result.get('url', 'بدون رابط')}")
            print(f"     المصدر: {result.get('source', 'غير معروف')}")
            print()
        
        # عرض الإحصائيات
        stats = intelligent_search.get_usage_stats()
        print("📊 إحصائيات الاستخدام:")
        print(f"  • إجمالي البحثات: {stats['total_searches']}")
        print(f"  • البحثات الناجحة: {stats['successful_searches']}")
        print(f"  • البحثات الفاشلة: {stats['failed_searches']}")
        print(f"  • المحركات المستخدمة: {stats['engines_used']}")
        
        return len(results) > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار البحث الذكي: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_internal_links():
    """اختبار نظام الروابط الداخلية المحسن"""
    print("🔗 اختبار نظام الروابط الداخلية المحسن...")
    
    try:
        from modules.internal_links_manager import internal_links_manager
        
        sample_content = """
        أعلنت شركة Sony عن إصدار جديد للعبة God of War على منصة PlayStation 5.
        كما أكدت Microsoft أن لعبة Halo ستكون متاحة على Xbox Series X قريباً.
        وفي الوقت نفسه، تعمل Nintendo على تطوير ألعاب جديدة لجهاز Nintendo Switch.
        """
        
        enhanced_content, links_data = internal_links_manager.add_internal_links_to_content(sample_content)
        
        print(f"✅ تم إضافة {links_data['total_links']} رابط داخلي")
        print(f"📊 الشركات: {len(links_data['companies'])}")
        print(f"🎮 الألعاب: {len(links_data['games'])}")
        print(f"🖥️ المنصات: {len(links_data['platforms'])}")
        print(f"👤 الأشخاص: {len(links_data['persons'])}")
        
        print("\n📝 المحتوى المحسن:")
        print(enhanced_content)
        
        # فحص أن الروابط لا تؤدي إلى صفحات فارغة
        import re
        links = re.findall(r'href="([^"]*)"', enhanced_content)
        print(f"\n🔍 فحص الروابط ({len(links)} رابط):")
        
        for link in links:
            if link.startswith('http'):
                print(f"  ✅ رابط خارجي: {link}")
            elif link.startswith('/'):
                print(f"  ⚠️ رابط داخلي (تم تحويله لرابط بحث): {link}")
            else:
                print(f"  ✅ رابط محسن: {link}")
        
        return links_data['total_links'] > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الروابط الداخلية: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_content_generation():
    """اختبار توليد المحتوى مع التحسينات الجديدة"""
    print("📝 اختبار توليد المحتوى مع التحسينات...")

    try:
        from modules.content_generator import ContentGenerator

        generator = ContentGenerator()

        # محتوى تجريبي
        source_content = {
            'title': 'أحدث أخبار PlayStation 5',
            'content': 'أعلنت Sony عن مجموعة جديدة من الألعاب الحصرية لجهاز PlayStation 5',
            'summary': 'أخبار جديدة عن ألعاب PlayStation 5',
            'keywords': ['PlayStation 5', 'Sony', 'ألعاب حصرية']
        }

        # توليد المقال
        article = await generator.generate_article(
            source_content=source_content,
            content_type='news',
            dialect='egyptian'
        )
        
        if article:
            print("✅ تم توليد المقال بنجاح")
            print(f"📋 العنوان: {article.get('title', 'بدون عنوان')}")
            print(f"🔗 عدد الروابط الداخلية: {len(re.findall(r'<a href=', article.get('content', '')))}")
            print(f"🔍 عدد الكلمات المفتاحية: {len(article.get('keywords', []))}")
            
            # فحص وجود قسم الكلمات المفتاحية
            content = article.get('content', '')
            if 'الكلمات المفتاحية:' in content:
                print("✅ تم إضافة قسم الكلمات المفتاحية")
            else:
                print("⚠️ لم يتم إضافة قسم الكلمات المفتاحية")
            
            # فحص الأخطاء الإملائية الطبيعية
            if 'هاذا' in content or 'الي' in content or 'عشان' in content:
                print("✅ تم تطبيق الأخطاء الإملائية الطبيعية")
            else:
                print("⚠️ لم يتم تطبيق الأخطاء الإملائية الطبيعية")
            
            return True
        else:
            print("❌ فشل في توليد المقال")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار توليد المحتوى: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_web_scraper():
    """اختبار نظام الاستخراج مع البحث الجديد"""
    print("🌐 اختبار نظام الاستخراج مع البحث الجديد...")
    
    try:
        from modules.advanced_web_scraper import AdvancedWebScraper
        
        scraper = AdvancedWebScraper()
        
        # اختبار البحث والاستخراج
        results = await scraper.search_and_extract(
            query="gaming news today",
            max_results=3
        )
        
        if results:
            print(f"✅ تم استخراج {len(results)} نتيجة")
            
            for i, result in enumerate(results, 1):
                print(f"  {i}. {result.get('title', 'بدون عنوان')}")
                print(f"     المصدر: {result.get('source', 'غير معروف')}")
                content_length = len(result.get('content', ''))
                print(f"     طول المحتوى: {content_length} حرف")
                print()
            
            return True
        else:
            print("⚠️ لم يتم العثور على نتائج")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستخراج: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار الأنظمة الجديدة")
    print("="*60)
    
    tests = [
        ("البحث الذكي البديل", test_intelligent_search()),
        ("الروابط الداخلية المحسنة", test_internal_links()),
        ("توليد المحتوى المحسن", test_content_generation()),
        ("الاستخراج مع البحث الجديد", test_web_scraper())
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔄 اختبار {test_name}...")
        print("-" * 40)
        
        try:
            if asyncio.iscoroutine(test_func):
                result = await test_func
            else:
                result = test_func
            
            results[test_name] = result
            
            if result:
                print(f"✅ نجح اختبار {test_name}")
            else:
                print(f"❌ فشل اختبار {test_name}")
                
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
        
        print("-" * 40)
    
    # النتائج النهائية
    print("\n📊 النتائج النهائية:")
    print("="*60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"  {status} - {test_name}")
    
    print(f"\n🎯 النتيجة الإجمالية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الأنظمة تعمل بشكل صحيح!")
    elif passed > total // 2:
        print("⚠️ معظم الأنظمة تعمل - يحتاج بعض التحسين")
    else:
        print("❌ يحتاج إصلاحات إضافية")

if __name__ == "__main__":
    asyncio.run(main())
