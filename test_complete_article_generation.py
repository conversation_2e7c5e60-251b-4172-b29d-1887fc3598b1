#!/usr/bin/env python3
"""
اختبار شامل لإنشاء ونشر مقال كامل مع الصور على بلوجر
يتخطى جميع الخطوات ويظهر النتيجة النهائية
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Optional

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.content_generator import ContentGenerator
from modules.publisher import PublisherManager
from modules.smart_image_manager import smart_image_manager
from modules.internal_links_manager import internal_links_manager
from modules.general_article_image_generator import general_article_image_generator
from modules.smart_image_compositor import smart_image_compositor
from config.settings import BotConfig


class CompleteArticleGenerator:
    """مولد المقالات الشامل مع النشر"""
    
    def __init__(self):
        self.content_generator = ContentGenerator()
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'steps': {},
            'final_result': None
        }
        
        # إعداد الناشر
        blogger_config = {
            'client_id': BotConfig.BLOGGER_CLIENT_ID,
            'client_secret': BotConfig.BLOGGER_CLIENT_SECRET,
            'blog_id': BotConfig.BLOGGER_BLOG_ID
        }
        self.publisher = PublisherManager(blogger_config, None)
        
        logger.info("🚀 تم تهيئة مولد المقالات الشامل")

    async def generate_and_publish_complete_article(self):
        """إنشاء ونشر مقال كامل"""
        logger.info("=" * 60)
        logger.info("🎯 بدء إنشاء ونشر مقال كامل مع جميع التحسينات")
        logger.info("=" * 60)
        
        try:
            # الخطوة 1: إنشاء محتوى المقال
            article_data = await self._create_sample_article()
            if not article_data:
                logger.error("❌ فشل في إنشاء المقال")
                return False
            
            # الخطوة 2: تحديد نوع المقال وإنشاء الصور المناسبة
            images_data = await self._generate_appropriate_images(article_data)
            
            # الخطوة 3: تحسين المحتوى بالروابط الداخلية
            enhanced_content = await self._enhance_content_with_links(article_data)
            
            # الخطوة 4: تجميع المقال النهائي
            final_article = await self._assemble_final_article(article_data, images_data, enhanced_content)
            
            # الخطوة 5: نشر المقال على بلوجر
            published_result = await self._publish_to_blogger(final_article)
            
            # الخطوة 6: إنشاء التقرير النهائي
            self._generate_final_report(published_result)
            
            return published_result is not None
            
        except Exception as e:
            logger.error(f"❌ خطأ في العملية الشاملة: {e}")
            return False

    async def _create_sample_article(self) -> Optional[Dict]:
        """إنشاء مقال تجريبي"""
        logger.info("📝 الخطوة 1: إنشاء محتوى المقال...")
        
        try:
            # محتوى تجريبي متنوع
            sample_content = {
                'title': 'أفضل 5 ألعاب مجانية على Steam يجب أن تجربها في 2025',
                'content': '''
                مع تزايد شعبية الألعاب المجانية، أصبحت منصة Steam تضم مجموعة رائعة من الألعاب المجانية عالية الجودة. 
                في هذا المقال، سنستعرض أفضل 5 ألعاب مجانية متاحة على Steam والتي تستحق وقتك.

                ## 1. Dota 2
                لعبة MOBA الشهيرة من Valve تعتبر واحدة من أفضل الألعاب التنافسية في العالم. 
                مع بطولات بجوائز تصل لملايين الدولارات، تقدم Dota 2 تجربة لعب عميقة ومعقدة.

                ## 2. Counter-Strike 2
                النسخة المحدثة من CS:GO تقدم تجربة إطلاق نار تكتيكية لا مثيل لها. 
                مع رسوميات محسنة ومحرك Source 2 الجديد.

                ## 3. Team Fortress 2
                لعبة إطلاق النار الجماعية الكلاسيكية من Valve تحتفظ بشعبيتها بفضل أسلوب اللعب الفريد والشخصيات المميزة.

                ## 4. Warframe
                لعبة أكشن مستقبلية تجمع بين القتال السريع والتخصيص العميق للشخصيات والأسلحة.

                ## 5. Path of Exile
                لعبة RPG مظلمة تقدم نظام مهارات معقد وقصة عميقة في عالم مليء بالوحوش والكنوز.

                ## الخلاصة
                هذه الألعاب تثبت أن الألعاب المجانية يمكن أن تكون بجودة الألعاب المدفوعة، بل وأحياناً أفضل منها.
                ''',
                'keywords': ['ألعاب مجانية', 'Steam', 'Dota 2', 'Counter-Strike', 'أفضل ألعاب'],
                'content_type': 'قائمة_ألعاب',
                'source_url': 'https://store.steampowered.com',
                'published_date': datetime.now()
            }
            
            # توليد المقال باستخدام مولد المحتوى
            generated_article = self.content_generator.generate_article(
                sample_content, 
                'قائمة_ألعاب', 
                'مصري'
            )
            
            if generated_article and 'error' not in generated_article:
                self.test_results['steps']['article_creation'] = {
                    'status': 'success',
                    'title': generated_article['title'],
                    'content_length': len(generated_article['content']),
                    'keywords_count': len(generated_article.get('keywords', []))
                }
                logger.info(f"✅ تم إنشاء المقال: {generated_article['title']}")
                return generated_article
            else:
                logger.error("❌ فشل في توليد المقال")
                return None
                
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء المقال: {e}")
            return None

    async def _generate_appropriate_images(self, article_data: Dict) -> Optional[Dict]:
        """إنشاء الصور المناسبة للمقال"""
        logger.info("🎨 الخطوة 2: إنشاء الصور المناسبة...")
        
        try:
            images_result = {
                'main_image': None,
                'additional_images': [],
                'generation_method': None
            }
            
            # تحديد نوع المقال
            article_type = smart_image_manager._determine_article_type(article_data)
            logger.info(f"🎯 نوع المقال المحدد: {article_type}")
            
            # إنشاء الصورة الرئيسية
            if article_type in ['general_list', 'comparison']:
                # استخدام نظام المقالات العامة
                logger.info("🎨 استخدام نظام المقالات العامة...")
                main_image = await general_article_image_generator.generate_image_for_general_article(article_data)
                
                if main_image:
                    images_result['main_image'] = main_image
                    images_result['generation_method'] = 'general_article_system'
                    logger.info("✅ تم إنشاء صورة بنظام المقالات العامة")
                
            else:
                # استخدام النظام الذكي العادي
                logger.info("🎨 استخدام النظام الذكي العادي...")
                main_image = await smart_image_manager.generate_smart_image_for_article(article_data)
                
                if main_image:
                    images_result['main_image'] = main_image
                    images_result['generation_method'] = 'smart_image_system'
                    logger.info("✅ تم إنشاء صورة بالنظام الذكي")
            
            # محاولة إنشاء صور إضافية للألعاب المذكورة
            mentioned_games = ['Dota 2', 'Counter-Strike 2', 'Team Fortress 2']
            for game in mentioned_games[:2]:  # أول لعبتين فقط
                try:
                    game_composite = await smart_image_compositor.create_smart_composite(article_data, game)
                    if game_composite:
                        images_result['additional_images'].append({
                            'game': game,
                            'image': game_composite
                        })
                        logger.info(f"✅ تم إنشاء صورة مركبة لـ {game}")
                except Exception as e:
                    logger.warning(f"⚠️ فشل في إنشاء صورة لـ {game}: {e}")
            
            # تسجيل النتائج
            self.test_results['steps']['image_generation'] = {
                'status': 'success' if images_result['main_image'] else 'partial',
                'main_image_created': images_result['main_image'] is not None,
                'additional_images_count': len(images_result['additional_images']),
                'generation_method': images_result['generation_method']
            }
            
            return images_result
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء الصور: {e}")
            self.test_results['steps']['image_generation'] = {
                'status': 'failed',
                'error': str(e)
            }
            return None

    async def _enhance_content_with_links(self, article_data: Dict) -> str:
        """تحسين المحتوى بالروابط الداخلية"""
        logger.info("🔗 الخطوة 3: إضافة الروابط المحسنة...")
        
        try:
            original_content = article_data.get('content', '')
            
            # إضافة الروابط المحسنة
            enhanced_content, links_data = internal_links_manager.add_internal_links_to_content(original_content)
            
            # تسجيل النتائج
            self.test_results['steps']['link_enhancement'] = {
                'status': 'success',
                'links_added': links_data['total_links'],
                'companies_linked': len(links_data['companies']),
                'games_linked': len(links_data['games']),
                'platforms_linked': len(links_data['platforms'])
            }
            
            logger.info(f"✅ تم إضافة {links_data['total_links']} رابط محسن")
            return enhanced_content
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحسين الروابط: {e}")
            self.test_results['steps']['link_enhancement'] = {
                'status': 'failed',
                'error': str(e)
            }
            return article_data.get('content', '')

    async def _assemble_final_article(self, article_data: Dict, images_data: Dict, enhanced_content: str) -> Dict:
        """تجميع المقال النهائي"""
        logger.info("📋 الخطوة 4: تجميع المقال النهائي...")
        
        try:
            final_article = article_data.copy()
            
            # تحديث المحتوى المحسن
            final_article['content'] = enhanced_content
            
            # إضافة الصور
            if images_data and images_data.get('main_image'):
                final_article['image_urls'] = [images_data['main_image']['url']]
                final_article['image_metadata'] = [images_data['main_image']]
                final_article['thumbnail_url'] = images_data['main_image']['url']
                final_article['thumbnail_metadata'] = images_data['main_image']
                
                # إضافة الصور الإضافية
                for additional_img in images_data.get('additional_images', []):
                    final_article['image_urls'].append(additional_img['image']['url'])
                    final_article['image_metadata'].append(additional_img['image'])
            
            # إضافة معلومات إضافية
            final_article['enhanced_features'] = {
                'smart_links': True,
                'ai_generated_images': True,
                'professional_layout': True,
                'seo_optimized': True
            }
            
            # تحسين العنوان للنشر
            final_article['title'] = f"🎮 {final_article['title']}"
            
            self.test_results['steps']['article_assembly'] = {
                'status': 'success',
                'final_title': final_article['title'],
                'images_included': len(final_article.get('image_urls', [])),
                'content_length': len(final_article['content'])
            }
            
            logger.info("✅ تم تجميع المقال النهائي بنجاح")
            return final_article
            
        except Exception as e:
            logger.error(f"❌ خطأ في تجميع المقال: {e}")
            return article_data

    async def _publish_to_blogger(self, final_article: Dict) -> Optional[Dict]:
        """نشر المقال على بلوجر"""
        logger.info("📤 الخطوة 5: نشر المقال على بلوجر...")
        
        try:
            # اختبار الاتصال أولاً
            connection_test = await self.publisher.test_all_connections()
            
            if not connection_test.get('blogger', False):
                logger.error("❌ فشل في الاتصال ببلوجر")
                self.test_results['steps']['publishing'] = {
                    'status': 'failed',
                    'error': 'blogger_connection_failed'
                }
                return None
            
            logger.info("✅ تم التحقق من اتصال بلوجر")
            
            # نشر المقال
            publish_result = await self.publisher.publish_article(final_article, 'blogger')
            
            if publish_result and publish_result.get('success'):
                blogger_url = publish_result.get('blogger_url')
                
                self.test_results['steps']['publishing'] = {
                    'status': 'success',
                    'blogger_url': blogger_url,
                    'publish_time': datetime.now().isoformat()
                }
                
                logger.info("🎉 تم نشر المقال بنجاح!")
                logger.info(f"🔗 رابط المقال: {blogger_url}")
                
                return {
                    'success': True,
                    'url': blogger_url,
                    'title': final_article['title'],
                    'publish_time': datetime.now().isoformat()
                }
            else:
                error_msg = publish_result.get('error', 'unknown_error') if publish_result else 'no_result'
                logger.error(f"❌ فشل في نشر المقال: {error_msg}")
                
                self.test_results['steps']['publishing'] = {
                    'status': 'failed',
                    'error': error_msg
                }
                return None
                
        except Exception as e:
            logger.error(f"❌ خطأ في النشر: {e}")
            self.test_results['steps']['publishing'] = {
                'status': 'failed',
                'error': str(e)
            }
            return None

    def _generate_final_report(self, published_result: Optional[Dict]):
        """إنشاء التقرير النهائي"""
        logger.info("📊 الخطوة 6: إنشاء التقرير النهائي...")
        
        # تحديث النتيجة النهائية
        self.test_results['final_result'] = published_result
        
        # حساب الإحصائيات
        total_steps = len(self.test_results['steps'])
        successful_steps = sum(1 for step in self.test_results['steps'].values() if step.get('status') == 'success')
        
        success_rate = (successful_steps / total_steps * 100) if total_steps > 0 else 0
        
        # حفظ التقرير
        report_filename = f"complete_article_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        # طباعة النتائج
        logger.info("=" * 60)
        logger.info("🎯 تقرير الاختبار الشامل")
        logger.info("=" * 60)
        logger.info(f"📊 إجمالي الخطوات: {total_steps}")
        logger.info(f"✅ خطوات ناجحة: {successful_steps}")
        logger.info(f"📈 معدل النجاح: {success_rate:.1f}%")
        
        # تفاصيل كل خطوة
        for step_name, step_data in self.test_results['steps'].items():
            status_icon = "✅" if step_data.get('status') == 'success' else "❌"
            logger.info(f"{status_icon} {step_name}: {step_data.get('status', 'unknown')}")
        
        if published_result:
            logger.info("🎉 النتيجة النهائية: تم نشر المقال بنجاح!")
            logger.info(f"🔗 رابط المقال: {published_result.get('url', 'غير متوفر')}")
        else:
            logger.error("❌ النتيجة النهائية: فشل في نشر المقال")
        
        logger.info(f"📄 التقرير المفصل: {report_filename}")
        logger.info("=" * 60)


async def main():
    """الدالة الرئيسية"""
    logger.info("🚀 بدء الاختبار الشامل لإنشاء ونشر مقال كامل")
    
    generator = CompleteArticleGenerator()
    success = await generator.generate_and_publish_complete_article()
    
    if success:
        logger.info("🎉 اكتمل الاختبار الشامل بنجاح!")
    else:
        logger.error("❌ فشل الاختبار الشامل")
    
    return success


if __name__ == "__main__":
    asyncio.run(main())
